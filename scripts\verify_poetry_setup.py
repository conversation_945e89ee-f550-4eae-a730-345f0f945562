#!/usr/bin/env python3
"""
Verification script to ensure Poetry setup works correctly with Flask CLI.
"""
import subprocess
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent


def run_command(command, cwd=None, check_output=True):
    """Run a command and return success status."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd or project_root,
            capture_output=check_output,
            text=True,
            timeout=30
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)


def test_poetry_installation():
    """Test if Poetry is installed and working."""
    print("Testing Poetry installation...")
    success, stdout, stderr = run_command("poetry --version")
    if success:
        print(f"✓ Poetry is installed: {stdout.strip()}")
        return True
    else:
        print(f"✗ Poetry not found: {stderr}")
        return False


def test_poetry_install():
    """Test Poetry dependency installation."""
    print("Testing Poetry dependency installation...")
    success, stdout, stderr = run_command("poetry install --dry-run")
    if success:
        print("✓ Poetry dependencies can be installed")
        return True
    else:
        print(f"✗ Poetry install failed: {stderr}")
        return False


def test_flask_cli():
    """Test Flask CLI commands through Poetry."""
    print("Testing Flask CLI through Poetry...")
    
    # Test basic Flask command
    success, stdout, stderr = run_command("poetry run flask --help")
    if not success:
        print(f"✗ Flask CLI not accessible: {stderr}")
        return False
    
    print("✓ Flask CLI accessible through Poetry")
    
    # Test custom commands
    success, stdout, stderr = run_command("poetry run flask --help")
    if success and ("init-db" in stdout or "create-admin" in stdout):
        print("✓ Custom Flask commands available")
        return True
    else:
        print("⚠️  Custom Flask commands may not be registered yet")
        return True  # This is OK for initial setup


def test_timester_agent_script():
    """Test the timester-agent script entry point."""
    print("Testing timester-agent script...")
    success, stdout, stderr = run_command("poetry run timester-agent --help")
    if success:
        print("✓ timester-agent script works")
        return True
    else:
        print(f"⚠️  timester-agent script not working yet: {stderr}")
        return True  # This is OK if dependencies aren't installed yet


def test_pytest():
    """Test pytest through Poetry."""
    print("Testing pytest through Poetry...")
    success, stdout, stderr = run_command("poetry run pytest --version")
    if success:
        print("✓ pytest accessible through Poetry")
        return True
    else:
        print(f"✗ pytest not accessible: {stderr}")
        return False


def test_code_quality_tools():
    """Test code quality tools through Poetry."""
    print("Testing code quality tools...")
    
    tools = ["black", "flake8", "mypy"]
    all_good = True
    
    for tool in tools:
        success, stdout, stderr = run_command(f"poetry run {tool} --version")
        if success:
            print(f"✓ {tool} accessible through Poetry")
        else:
            print(f"✗ {tool} not accessible: {stderr}")
            all_good = False
    
    return all_good


def main():
    """Run all verification tests."""
    print("🔍 Verifying Poetry setup for Timester")
    print("=" * 50)
    
    tests = [
        ("Poetry Installation", test_poetry_installation),
        ("Poetry Dependencies", test_poetry_install),
        ("Flask CLI", test_flask_cli),
        ("Timester Agent Script", test_timester_agent_script),
        ("Pytest", test_pytest),
        ("Code Quality Tools", test_code_quality_tools),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"  {status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All tests passed! Poetry setup is working correctly.")
        print("\nNext steps:")
        print("1. Run 'poetry install' to install dependencies")
        print("2. Run 'poetry run python scripts/setup_dev.py' to set up the development environment")
        print("3. Start developing with 'poetry run flask run'")
    else:
        print("⚠️  Some tests failed. Please check the Poetry configuration.")
        print("\nTroubleshooting:")
        print("1. Ensure Poetry is installed: curl -sSL https://install.python-poetry.org | python3 -")
        print("2. Check pyproject.toml for syntax errors")
        print("3. Try 'poetry install' to install dependencies")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
