"""
Health check and system status routes.
"""
from flask import Blueprint, jsonify
from timester.app import db

health_bp = Blueprint('health', __name__)


@health_bp.route('/health', methods=['GET'])
def health_check():
    """Basic health check endpoint."""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except Exception:
        db_status = 'unhealthy'
    
    return jsonify({
        'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
        'database': db_status,
        'service': 'timester'
    }), 200 if db_status == 'healthy' else 503


@health_bp.route('/version', methods=['GET'])
def version():
    """Get application version information."""
    return jsonify({
        'name': 'Timester',
        'version': '0.1.0',
        'description': 'Team time tracking application'
    }), 200
