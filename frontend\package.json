{"name": "timester-frontend", "version": "0.1.0", "description": "Frontend for Timester time tracking application", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "axios": "^1.5.0", "pinia": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "vite": "^4.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0"}}