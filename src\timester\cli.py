"""
Command-line interface for Timester management tasks.
"""
import click
from flask.cli import with_appcontext
from timester.app import db
from timester.models.user import User
from timester.models.task import Task


@click.command()
@with_appcontext
def init_db():
    """Initialize the database."""
    db.create_all()
    click.echo('Database initialized.')


@click.command()
@with_appcontext
def create_admin():
    """Create an administrator user."""
    username = click.prompt('Username')
    email = click.prompt('Email')
    password = click.prompt('Password', hide_input=True, confirmation_prompt=True)
    
    # Check if user already exists
    existing_user = User.query.filter(
        (User.username == username) | (User.email == email)
    ).first()
    
    if existing_user:
        click.echo('Error: User with this username or email already exists.')
        return
    
    # Create admin user
    admin = User(
        username=username,
        email=email,
        password=password,
        role='administrator'
    )
    
    db.session.add(admin)
    db.session.commit()
    
    click.echo(f'Administrator user "{username}" created successfully.')


@click.command()
@with_appcontext
def create_sample_tasks():
    """Create sample tasks for development."""
    sample_tasks = [
        {
            'name': 'Development',
            'description': 'Software development and coding tasks'
        },
        {
            'name': 'Testing',
            'description': 'Quality assurance and testing activities'
        },
        {
            'name': 'Documentation',
            'description': 'Writing and updating documentation'
        },
        {
            'name': 'Meetings',
            'description': 'Team meetings and client calls'
        },
        {
            'name': 'Research',
            'description': 'Research and learning activities'
        }
    ]
    
    for task_data in sample_tasks:
        # Check if task already exists
        existing_task = Task.query.filter_by(name=task_data['name']).first()
        if not existing_task:
            task = Task(
                name=task_data['name'],
                description=task_data['description']
            )
            db.session.add(task)
    
    db.session.commit()
    click.echo('Sample tasks created successfully.')


@click.command()
@click.argument('username')
@with_appcontext
def reset_password(username):
    """Reset a user's password."""
    user = User.query.filter_by(username=username).first()
    
    if not user:
        click.echo(f'Error: User "{username}" not found.')
        return
    
    new_password = click.prompt('New password', hide_input=True, confirmation_prompt=True)
    user.set_password(new_password)
    db.session.commit()
    
    click.echo(f'Password reset for user "{username}".')


@click.command()
@with_appcontext
def list_users():
    """List all users."""
    users = User.query.all()
    
    if not users:
        click.echo('No users found.')
        return
    
    click.echo('Users:')
    click.echo('-' * 50)
    for user in users:
        status = 'Active' if user.is_active else 'Inactive'
        click.echo(f'{user.id:3d} | {user.username:20s} | {user.role:15s} | {status}')


def register_commands(app):
    """Register CLI commands with the Flask app."""
    app.cli.add_command(init_db)
    app.cli.add_command(create_admin)
    app.cli.add_command(create_sample_tasks)
    app.cli.add_command(reset_password)
    app.cli.add_command(list_users)

