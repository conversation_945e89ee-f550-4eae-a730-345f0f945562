"""
Main Flask application factory and configuration.
"""

import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()


def create_app(config_name=None):
    """Create and configure the Flask application."""
    app = Flask(__name__)

    # Load configuration
    if config_name is None:
        config_name = os.getenv("FLASK_ENV", "development")

    app.config.from_object(f"timester.config.{config_name.title()}Config")

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    CORS(app)

    # Register blueprints
    from timester.routes.auth import auth_bp
    from timester.routes.time_tracking import time_bp
    from timester.routes.admin import admin_bp
    from timester.routes.screenshots import screenshots_bp
    from timester.routes.health import health_bp

    app.register_blueprint(health_bp, url_prefix="/api")
    app.register_blueprint(auth_bp, url_prefix="/api/auth")
    app.register_blueprint(time_bp, url_prefix="/api/time")
    app.register_blueprint(admin_bp, url_prefix="/api/admin")
    app.register_blueprint(screenshots_bp, url_prefix="/api/screenshots")

    # Import models to ensure they are registered with SQLAlchemy
    from timester.models import User, Task, TimeEntry, Screenshot  # noqa: F401

    # Register CLI commands
    from timester.cli import register_commands

    register_commands(app)

    return app


if __name__ == "__main__":
    app = create_app()
    app.run(debug=True)
