"""
Main desktop agent application for screenshot capture and upload.
"""
import os
import sys
import time
import json
import logging
import threading
from datetime import datetime
from pathlib import Path

from .screenshot_capture import ScreenshotCapture
from .api_client import ApiClient
from .config import AgentConfig


class TimesterAgent:
    """Main desktop agent class."""
    
    def __init__(self, config_path=None):
        """Initialize the agent."""
        self.config = AgentConfig(config_path)
        self.screenshot_capture = ScreenshotCapture()
        self.api_client = ApiClient(
            self.config.server_url,
            self.config.username,
            self.config.password
        )
        
        self.running = False
        self.capture_thread = None
        
        # Setup logging
        self._setup_logging()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Timester Agent initialized")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_dir = Path.home() / '.timester' / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / 'agent.log'
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def start(self):
        """Start the agent."""
        if self.running:
            self.logger.warning("Agent is already running")
            return
        
        self.logger.info("Starting Timester Agent")
        
        # Authenticate with the server
        if not self.api_client.authenticate():
            self.logger.error("Failed to authenticate with server")
            return False
        
        self.running = True
        
        # Start screenshot capture thread
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        
        self.logger.info("Agent started successfully")
        return True
    
    def stop(self):
        """Stop the agent."""
        if not self.running:
            return
        
        self.logger.info("Stopping Timester Agent")
        self.running = False
        
        if self.capture_thread:
            self.capture_thread.join(timeout=5)
        
        self.logger.info("Agent stopped")
    
    def _capture_loop(self):
        """Main capture loop running in a separate thread."""
        self.logger.info(f"Starting capture loop with {self.config.capture_interval} second intervals")
        
        while self.running:
            try:
                # Capture screenshot
                screenshot_data = self.screenshot_capture.capture()
                
                if screenshot_data:
                    # Upload screenshot
                    success = self.api_client.upload_screenshot(
                        screenshot_data['image_data'],
                        screenshot_data['timestamp'],
                        screenshot_data.get('activity_level'),
                        screenshot_data.get('active_window_title')
                    )
                    
                    if success:
                        self.logger.debug("Screenshot uploaded successfully")
                    else:
                        self.logger.warning("Failed to upload screenshot")
                else:
                    self.logger.warning("Failed to capture screenshot")
                
            except Exception as e:
                self.logger.error(f"Error in capture loop: {e}")
            
            # Wait for next capture
            time.sleep(self.config.capture_interval)
    
    def run_forever(self):
        """Run the agent until interrupted."""
        if not self.start():
            return
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
        finally:
            self.stop()


def main():
    """Main entry point for the desktop agent."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Timester Desktop Agent')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    
    args = parser.parse_args()
    
    agent = TimesterAgent(args.config)
    
    if args.daemon:
        # TODO: Implement proper daemon mode
        agent.run_forever()
    else:
        agent.run_forever()


if __name__ == '__main__':
    main()
