"""
Time entry model for tracking work sessions.
"""

from datetime import datetime
from timester.app import db


class TimeEntry(db.Model):
    """Time entry model for tracking work sessions."""

    __tablename__ = "time_entries"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>("users.id"), nullable=False)
    task_id = db.Column(db.Integer, db.ForeignKey("tasks.id"), nullable=False)
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime)
    duration_seconds = db.Column(db.Integer)
    description = db.Column(db.Text)
    is_manual = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Indexes for better query performance
    __table_args__ = (
        db.Index("idx_time_entries_user_date", "user_id", "start_time"),
        db.Index("idx_time_entries_task_date", "task_id", "start_time"),
    )

    def __init__(
        self, user_id, task_id, start_time=None, description=None, is_manual=False
    ):
        """Initialize a new time entry."""
        self.user_id = user_id
        self.task_id = task_id
        self.start_time = start_time or datetime.utcnow()
        self.description = description
        self.is_manual = is_manual

    def stop_timer(self, end_time=None):
        """Stop the timer and calculate duration."""
        if self.end_time is not None:
            raise ValueError("Timer is already stopped")

        self.end_time = end_time or datetime.utcnow()
        self.duration_seconds = int((self.end_time - self.start_time).total_seconds())

    def get_duration_hours(self):
        """Get duration in hours (for backward compatibility)."""
        if self.duration_seconds is None:
            return None
        return self.duration_seconds / 3600.0

    def is_active(self):
        """Check if this time entry is currently active (timer running)."""
        return self.end_time is None

    def to_dict(self):
        """Convert time entry to dictionary representation."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "task_id": self.task_id,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_seconds": self.duration_seconds,
            "description": self.description,
            "is_manual": self.is_manual,
            "is_active": self.is_active(),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }

    def __repr__(self):
        """String representation of the time entry."""
        status = "active" if self.is_active() else "completed"
        return f"<TimeEntry {self.id} ({status})>"
