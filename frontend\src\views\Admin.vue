<template>
  <div class="admin">
    <div class="card">
      <div class="card-header">
        <h2>Admin Panel</h2>
      </div>

      <div class="admin-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="['tab-button', { active: activeTab === tab.id }]"
        >
          {{ tab.name }}
        </button>
      </div>

      <!-- Users Tab -->
      <div v-if="activeTab === 'users'" class="tab-content">
        <div class="section-header">
          <h3>User Management</h3>
          <button @click="showCreateUser = true" class="btn btn-primary">
            Add User
          </button>
        </div>

        <div class="users-table">
          <table class="table">
            <thead>
              <tr>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in users" :key="user.id">
                <td>{{ user.username }}</td>
                <td>{{ user.email }}</td>
                <td>{{ user.role }}</td>
                <td>
                  <span :class="user.is_active ? 'status-active' : 'status-inactive'">
                    {{ user.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td>{{ formatDate(user.created_at) }}</td>
                <td>
                  <button @click="editUser(user)" class="btn btn-secondary btn-sm">
                    Edit
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Tasks Tab -->
      <div v-if="activeTab === 'tasks'" class="tab-content">
        <div class="section-header">
          <h3>Task Management</h3>
          <button @click="showCreateTask = true" class="btn btn-primary">
            Add Task
          </button>
        </div>

        <div class="tasks-table">
          <table class="table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Description</th>
                <th>Status</th>
                <th>Total Hours</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="task in tasks" :key="task.id">
                <td>{{ task.name }}</td>
                <td>{{ task.description || '-' }}</td>
                <td>
                  <span :class="task.is_active ? 'status-active' : 'status-inactive'">
                    {{ task.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td>{{ formatDuration(task.total_seconds || 0, 'human') }}</td>
                <td>{{ formatDate(task.created_at) }}</td>
                <td>
                  <button @click="editTask(task)" class="btn btn-secondary btn-sm">
                    Edit
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Reports Tab -->
      <div v-if="activeTab === 'reports'" class="tab-content">
        <h3>Time Reports</h3>

        <!-- Duration Format Selector -->
        <DurationFormatSelector @format-changed="onFormatChanged" />

        <div class="report-filters">
          <div class="form-row">
            <div class="form-group">
              <label>User</label>
              <select v-model="reportFilters.user_id">
                <option value="">All Users</option>
                <option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.username }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label>Start Date</label>
              <input v-model="reportFilters.start_date" type="date" />
            </div>
            <div class="form-group">
              <label>End Date</label>
              <input v-model="reportFilters.end_date" type="date" />
            </div>
            <div class="form-group">
              <button @click="loadTimeEntries" class="btn btn-primary">
                Generate Report
              </button>
            </div>
          </div>
        </div>

        <div class="time-entries-table">
          <table class="table">
            <thead>
              <tr>
                <th>User</th>
                <th>Task</th>
                <th>Date</th>
                <th>Duration</th>
                <th>Description</th>
                <th>Type</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="entry in timeEntries" :key="entry.id">
                <td>{{ getUserName(entry.user_id) }}</td>
                <td>{{ getTaskName(entry.task_id) }}</td>
                <td>{{ formatDate(entry.start_time) }}</td>
                <td>{{ formatDuration(entry.duration_seconds, 'human') }}</td>
                <td>{{ entry.description || '-' }}</td>
                <td>
                  <span :class="entry.is_manual ? 'badge-manual' : 'badge-timer'">
                    {{ entry.is_manual ? 'Manual' : 'Timer' }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Create User Modal -->
    <div v-if="showCreateUser" class="modal-overlay" @click="showCreateUser = false">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>Create New User</h3>
          <button @click="showCreateUser = false" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="createUser">
          <div class="form-group">
            <label>Username</label>
            <input v-model="newUser.username" type="text" required />
          </div>
          <div class="form-group">
            <label>Email</label>
            <input v-model="newUser.email" type="email" required />
          </div>
          <div class="form-group">
            <label>Password</label>
            <input v-model="newUser.password" type="password" required />
          </div>
          <div class="form-group">
            <label>Role</label>
            <select v-model="newUser.role" required>
              <option value="team_member">Team Member</option>
              <option value="administrator">Administrator</option>
            </select>
          </div>
          <div class="modal-actions">
            <button type="button" @click="showCreateUser = false" class="btn btn-secondary">
              Cancel
            </button>
            <button type="submit" class="btn btn-primary" :disabled="loading">
              Create User
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Create Task Modal -->
    <div v-if="showCreateTask" class="modal-overlay" @click="showCreateTask = false">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>Create New Task</h3>
          <button @click="showCreateTask = false" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="createTask">
          <div class="form-group">
            <label>Name</label>
            <input v-model="newTask.name" type="text" required />
          </div>
          <div class="form-group">
            <label>Description</label>
            <textarea v-model="newTask.description" rows="3"></textarea>
          </div>
          <div class="modal-actions">
            <button type="button" @click="showCreateTask = false" class="btn btn-secondary">
              Cancel
            </button>
            <button type="submit" class="btn btn-primary" :disabled="loading">
              Create Task
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import api from '../services/api'
import { formatDuration, getDurationDisplayPreference } from '../utils/timeUtils'
import DurationFormatSelector from '../components/DurationFormatSelector.vue'

export default {
  name: 'Admin',
  components: {
    DurationFormatSelector
  },
  setup() {
    const activeTab = ref('users')
    const loading = ref(false)
    const users = ref([])
    const tasks = ref([])
    const timeEntries = ref([])
    const showCreateUser = ref(false)
    const showCreateTask = ref(false)
    const currentDurationFormat = ref(getDurationDisplayPreference())

    const tabs = [
      { id: 'users', name: 'Users' },
      { id: 'tasks', name: 'Tasks' },
      { id: 'reports', name: 'Reports' }
    ]

    const newUser = ref({
      username: '',
      email: '',
      password: '',
      role: 'team_member'
    })

    const newTask = ref({
      name: '',
      description: ''
    })

    const reportFilters = ref({
      user_id: '',
      start_date: '',
      end_date: ''
    })

    const loadUsers = async () => {
      try {
        const response = await api.get('/admin/users')
        users.value = response.data.users
      } catch (error) {
        console.error('Failed to load users:', error)
      }
    }

    const loadTasks = async () => {
      try {
        const response = await api.get('/admin/tasks')
        tasks.value = response.data.tasks
      } catch (error) {
        console.error('Failed to load tasks:', error)
      }
    }

    const loadTimeEntries = async () => {
      try {
        const params = new URLSearchParams()
        if (reportFilters.value.user_id) params.append('user_id', reportFilters.value.user_id)
        if (reportFilters.value.start_date) params.append('start_date', reportFilters.value.start_date)
        if (reportFilters.value.end_date) params.append('end_date', reportFilters.value.end_date)
        
        const response = await api.get(`/admin/time-entries?${params}`)
        timeEntries.value = response.data.time_entries
      } catch (error) {
        console.error('Failed to load time entries:', error)
      }
    }

    const createUser = async () => {
      loading.value = true
      try {
        await api.post('/admin/users', newUser.value)
        showCreateUser.value = false
        newUser.value = { username: '', email: '', password: '', role: 'team_member' }
        await loadUsers()
      } catch (error) {
        console.error('Failed to create user:', error)
      } finally {
        loading.value = false
      }
    }

    const createTask = async () => {
      loading.value = true
      try {
        await api.post('/admin/tasks', newTask.value)
        showCreateTask.value = false
        newTask.value = { name: '', description: '' }
        await loadTasks()
      } catch (error) {
        console.error('Failed to create task:', error)
      } finally {
        loading.value = false
      }
    }

    const editUser = (user) => {
      // TODO: Implement user editing
      console.log('Edit user:', user)
    }

    const editTask = (task) => {
      // TODO: Implement task editing
      console.log('Edit task:', task)
    }

    const getUserName = (userId) => {
      const user = users.value.find(u => u.id === userId)
      return user ? user.username : 'Unknown'
    }

    const getTaskName = (taskId) => {
      const task = tasks.value.find(t => t.id === taskId)
      return task ? task.name : 'Unknown'
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString()
    }

    const formatDurationDisplay = (seconds) => {
      return formatDuration(seconds, currentDurationFormat.value)
    }

    const onFormatChanged = (newFormat) => {
      currentDurationFormat.value = newFormat
    }

    onMounted(() => {
      loadUsers()
      loadTasks()
    })

    return {
      activeTab,
      loading,
      users,
      tasks,
      timeEntries,
      showCreateUser,
      showCreateTask,
      tabs,
      newUser,
      newTask,
      reportFilters,
      loadTimeEntries,
      createUser,
      createTask,
      editUser,
      editTask,
      getUserName,
      getTaskName,
      formatDate,
      formatDuration,
      formatDurationDisplay,
      onFormatChanged
    }
  }
}
</script>

<style scoped>
.admin-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 2rem;
}

.tab-button {
  padding: 1rem 2rem;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button:hover {
  background-color: #f8f9fa;
}

.tab-button.active {
  border-bottom-color: #3498db;
  color: #3498db;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.status-active {
  color: #27ae60;
  font-weight: bold;
}

.status-inactive {
  color: #e74c3c;
  font-weight: bold;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.report-filters {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.badge-manual {
  background-color: #f39c12;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.badge-timer {
  background-color: #27ae60;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}
</style>
