"""
Tests for time utility functions.

Run with: poetry run pytest tests/test_time_utils.py
"""
import pytest
from datetime import datetime, timedelta


# Mock the timeUtils functions for testing
def formatDurationHMS(seconds):
    """Format duration in seconds to HH:MM:SS format"""
    if not seconds or seconds < 0:
        return '00:00:00'
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"


def formatDurationDecimal(seconds):
    """Format duration in seconds to decimal hours"""
    if not seconds or seconds < 0:
        return '0.00'
    
    hours = seconds / 3600
    return f"{hours:.2f}"


def formatDurationHuman(seconds):
    """Format duration in seconds to human-readable format"""
    if not seconds or seconds < 0:
        return '0m'
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    
    if hours == 0:
        return f'{minutes}m'
    elif minutes == 0:
        return f'{hours}h'
    else:
        return f'{hours}h {minutes}m'


def parseDurationToSeconds(timeString):
    """Parse time string to seconds"""
    if not timeString or not isinstance(timeString, str):
        return 0
    
    parts = timeString.strip().split(':')
    
    if len(parts) == 2:
        # HH:MM format
        hours = int(parts[0]) if parts[0].isdigit() else 0
        minutes = int(parts[1]) if parts[1].isdigit() else 0
        return hours * 3600 + minutes * 60
    elif len(parts) == 3:
        # HH:MM:SS format
        hours = int(parts[0]) if parts[0].isdigit() else 0
        minutes = int(parts[1]) if parts[1].isdigit() else 0
        seconds = int(parts[2]) if parts[2].isdigit() else 0
        return hours * 3600 + minutes * 60 + seconds
    
    return 0


def getCurrentDuration(startTime):
    """Get current duration for an active timer"""
    if not startTime:
        return 0
    
    start = datetime.fromisoformat(startTime.replace('Z', '+00:00'))
    now = datetime.now(start.tzinfo) if start.tzinfo else datetime.utcnow()
    
    diff_seconds = (now - start).total_seconds()
    return max(0, int(diff_seconds))


class TestTimeUtils:
    """Test cases for time utility functions."""

    def test_format_duration_hms(self):
        """Test HH:MM:SS formatting."""
        assert formatDurationHMS(0) == '00:00:00'
        assert formatDurationHMS(30) == '00:00:30'
        assert formatDurationHMS(90) == '00:01:30'
        assert formatDurationHMS(3661) == '01:01:01'
        assert formatDurationHMS(7200) == '02:00:00'
        assert formatDurationHMS(9000) == '02:30:00'  # 2.5 hours
        
        # Edge cases
        assert formatDurationHMS(-10) == '00:00:00'
        assert formatDurationHMS(None) == '00:00:00'

    def test_format_duration_decimal(self):
        """Test decimal hours formatting."""
        assert formatDurationDecimal(0) == '0.00'
        assert formatDurationDecimal(1800) == '0.50'  # 30 minutes
        assert formatDurationDecimal(3600) == '1.00'  # 1 hour
        assert formatDurationDecimal(5400) == '1.50'  # 1.5 hours
        assert formatDurationDecimal(9000) == '2.50'  # 2.5 hours
        
        # Edge cases
        assert formatDurationDecimal(-10) == '0.00'
        assert formatDurationDecimal(None) == '0.00'

    def test_format_duration_human(self):
        """Test human-readable formatting."""
        assert formatDurationHuman(0) == '0m'
        assert formatDurationHuman(30) == '0m'  # Less than a minute
        assert formatDurationHuman(60) == '1m'
        assert formatDurationHuman(1800) == '30m'
        assert formatDurationHuman(3600) == '1h'
        assert formatDurationHuman(5400) == '1h 30m'
        assert formatDurationHuman(7200) == '2h'
        assert formatDurationHuman(9000) == '2h 30m'
        
        # Edge cases
        assert formatDurationHuman(-10) == '0m'
        assert formatDurationHuman(None) == '0m'

    def test_parse_duration_to_seconds(self):
        """Test parsing time strings to seconds."""
        # HH:MM format
        assert parseDurationToSeconds('01:30') == 5400  # 1.5 hours
        assert parseDurationToSeconds('02:00') == 7200  # 2 hours
        assert parseDurationToSeconds('00:30') == 1800  # 30 minutes
        
        # HH:MM:SS format
        assert parseDurationToSeconds('01:30:45') == 5445  # 1h 30m 45s
        assert parseDurationToSeconds('02:00:00') == 7200  # 2 hours
        assert parseDurationToSeconds('00:00:30') == 30    # 30 seconds
        
        # Edge cases
        assert parseDurationToSeconds('') == 0
        assert parseDurationToSeconds(None) == 0
        assert parseDurationToSeconds('invalid') == 0
        assert parseDurationToSeconds('25:70') == 0  # Invalid time
        
        # With whitespace
        assert parseDurationToSeconds(' 01:30 ') == 5400

    def test_get_current_duration(self):
        """Test current duration calculation."""
        # Test with a start time 1 hour ago
        one_hour_ago = (datetime.utcnow() - timedelta(hours=1)).isoformat()
        duration = getCurrentDuration(one_hour_ago)
        
        # Should be approximately 3600 seconds (1 hour), allow some variance
        assert 3590 <= duration <= 3610
        
        # Test with start time 30 minutes ago
        thirty_min_ago = (datetime.utcnow() - timedelta(minutes=30)).isoformat()
        duration = getCurrentDuration(thirty_min_ago)
        
        # Should be approximately 1800 seconds (30 minutes)
        assert 1790 <= duration <= 1810
        
        # Edge cases
        assert getCurrentDuration('') == 0
        assert getCurrentDuration(None) == 0

    def test_precision_and_accuracy(self):
        """Test that seconds-based calculations maintain precision."""
        # Test that we don't lose precision with seconds-based calculations
        test_durations = [1, 59, 60, 61, 3599, 3600, 3661, 7200, 9000]
        
        for seconds in test_durations:
            # Convert to hours and back to seconds
            hours = seconds / 3600.0
            back_to_seconds = int(hours * 3600)
            
            # Should maintain precision for reasonable durations
            assert back_to_seconds == seconds
            
            # Test formatting consistency
            hms_format = formatDurationHMS(seconds)
            parsed_back = parseDurationToSeconds(hms_format)
            
            # Should be able to round-trip through HH:MM:SS format
            # (Note: we lose seconds precision in HH:MM format)
            if ':' in hms_format and len(hms_format.split(':')) == 3:
                assert parsed_back == seconds

    def test_edge_cases_and_validation(self):
        """Test edge cases and input validation."""
        # Large numbers
        large_seconds = 86400 * 7  # 1 week in seconds
        assert formatDurationHMS(large_seconds) == '168:00:00'
        assert formatDurationDecimal(large_seconds) == '168.00'
        
        # Very small numbers
        assert formatDurationHMS(1) == '00:00:01'
        assert formatDurationDecimal(1) == '0.00'  # Rounds to 0.00
        
        # Boundary conditions
        assert formatDurationHMS(59) == '00:00:59'
        assert formatDurationHMS(60) == '00:01:00'
        assert formatDurationHMS(3599) == '00:59:59'
        assert formatDurationHMS(3600) == '01:00:00'
