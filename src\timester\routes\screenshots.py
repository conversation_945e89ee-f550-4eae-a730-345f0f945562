"""
Screenshot routes for upload and retrieval.
"""
import os
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, session
from werkzeug.utils import secure_filename
from timester.app import db
from timester.models.screenshot import Screenshot
from timester.services.storage import StorageService
from timester.utils.auth import login_required, admin_required

screenshots_bp = Blueprint('screenshots', __name__)


@screenshots_bp.route('/upload', methods=['POST'])
@login_required
def upload_screenshot():
    """Upload a screenshot from the desktop agent."""
    user_id = session['user_id']
    
    # Check if file is present
    if 'screenshot' not in request.files:
        return jsonify({'error': 'No screenshot file provided'}), 400
    
    file = request.files['screenshot']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    # Get metadata from form data
    timestamp_str = request.form.get('timestamp')
    activity_level = request.form.get('activity_level')
    active_window_title = request.form.get('active_window_title')
    
    if not timestamp_str:
        return jsonify({'error': 'Timestamp is required'}), 400
    
    try:
        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except ValueError:
        return jsonify({'error': 'Invalid timestamp format'}), 400
    
    # Validate file type and size
    allowed_extensions = {'png', 'jpg', 'jpeg'}
    file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
    
    if file_ext not in allowed_extensions:
        return jsonify({'error': 'Invalid file type. Only PNG and JPEG are allowed'}), 400
    
    # Check file size (limit from config)
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)
    
    max_size = 5 * 1024 * 1024  # 5MB default
    if file_size > max_size:
        return jsonify({'error': 'File size too large'}), 400
    
    try:
        # Generate unique filename
        unique_id = str(uuid.uuid4())
        timestamp_str = timestamp.strftime('%Y-%m-%d_%H-%M-%S')
        object_name = f"screenshots/user_{user_id}/{timestamp.strftime('%Y/%m/%d')}/{timestamp_str}_{unique_id}.{file_ext}"
        
        # Upload to storage
        storage_service = StorageService()
        success = storage_service.upload_file(file, object_name)
        
        if not success:
            return jsonify({'error': 'Failed to upload screenshot'}), 500
        
        # Save metadata to database
        screenshot = Screenshot(
            user_id=user_id,
            timestamp=timestamp,
            spaces_object_name=object_name,
            file_size_bytes=file_size,
            activity_level=activity_level,
            active_window_title=active_window_title
        )
        
        db.session.add(screenshot)
        db.session.commit()
        
        return jsonify({
            'message': 'Screenshot uploaded successfully',
            'screenshot': screenshot.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to process screenshot upload'}), 500


@screenshots_bp.route('/', methods=['GET'])
@admin_required
def get_screenshots():
    """Get screenshots with filtering options (admin only)."""
    # Parse query parameters
    user_id = request.args.get('user_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 100)
    
    # Build query
    query = Screenshot.query
    
    if user_id:
        query = query.filter_by(user_id=user_id)
    
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(Screenshot.timestamp >= start_dt)
        except ValueError:
            return jsonify({'error': 'Invalid start_date format'}), 400
    
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(Screenshot.timestamp <= end_dt)
        except ValueError:
            return jsonify({'error': 'Invalid end_date format'}), 400
    
    # Execute query with pagination
    screenshots = query.order_by(Screenshot.timestamp.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'screenshots': [screenshot.to_dict() for screenshot in screenshots.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': screenshots.total,
            'pages': screenshots.pages,
            'has_next': screenshots.has_next,
            'has_prev': screenshots.has_prev
        }
    }), 200


@screenshots_bp.route('/<int:screenshot_id>/url', methods=['GET'])
@admin_required
def get_screenshot_url(screenshot_id):
    """Get a signed URL for viewing a screenshot (admin only)."""
    screenshot = Screenshot.query.get_or_404(screenshot_id)
    
    try:
        storage_service = StorageService()
        signed_url = storage_service.generate_signed_url(
            screenshot.spaces_object_name,
            expiration=3600  # 1 hour
        )
        
        if not signed_url:
            return jsonify({'error': 'Failed to generate signed URL'}), 500
        
        return jsonify({
            'signed_url': signed_url,
            'expires_in': 3600
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to generate signed URL'}), 500


@screenshots_bp.route('/user/<int:user_id>', methods=['GET'])
@admin_required
def get_user_screenshots(user_id):
    """Get screenshots for a specific user (admin only)."""
    # Parse query parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 100)
    
    # Build query
    query = Screenshot.query.filter_by(user_id=user_id)
    
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(Screenshot.timestamp >= start_dt)
        except ValueError:
            return jsonify({'error': 'Invalid start_date format'}), 400
    
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(Screenshot.timestamp <= end_dt)
        except ValueError:
            return jsonify({'error': 'Invalid end_date format'}), 400
    
    # Execute query with pagination
    screenshots = query.order_by(Screenshot.timestamp.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'screenshots': [screenshot.to_dict() for screenshot in screenshots.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': screenshots.total,
            'pages': screenshots.pages,
            'has_next': screenshots.has_next,
            'has_prev': screenshots.has_prev
        }
    }), 200
