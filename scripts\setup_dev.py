#!/usr/bin/env python3
"""
Development setup script for Timester.
"""
import os
import sys
import subprocess
from pathlib import Path

# Add the src directory to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from timester.app import create_app, db
from timester.models.user import User
from timester.models.task import Task


def run_command(command, cwd=None):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(
            command, shell=True, check=True, capture_output=True, text=True, cwd=cwd
        )
        print(f"✓ {command}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"✗ {command}")
        print(f"Error: {e.stderr}")
        return None


def check_poetry():
    """Check if Poetry is installed."""
    try:
        result = subprocess.run(
            ["poetry", "--version"], capture_output=True, text=True, check=True
        )
        print(f"✓ Poetry found: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ Poetry not found")
        print("Please install Poetry: https://python-poetry.org/docs/#installation")
        return False


def setup_backend():
    """Set up the backend development environment."""
    print("Setting up backend...")

    # Create .env file if it doesn't exist
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"

    if not env_file.exists() and env_example.exists():
        print("Creating .env file from .env.example...")
        env_file.write_text(env_example.read_text())
        print("✓ Created .env file")
        print("⚠️  Please edit .env with your actual configuration values")

    # Install Python dependencies with Poetry
    print("Installing Python dependencies with Poetry...")
    if not run_command("poetry install"):
        print("Failed to install dependencies with Poetry")
        return

    # Initialize database
    print("Initializing database...")
    app = create_app("development")

    with app.app_context():
        try:
            # Create tables
            db.create_all()
            print("✓ Database tables created")

            # Create admin user if it doesn't exist
            admin = User.query.filter_by(username="admin").first()
            if not admin:
                admin = User(
                    username="admin",
                    email="<EMAIL>",
                    password="admin123",
                    role="administrator",
                )
                db.session.add(admin)
                print("✓ Created admin user (username: admin, password: admin123)")

            # Create sample team member
            team_user = User.query.filter_by(username="demo").first()
            if not team_user:
                team_user = User(
                    username="demo",
                    email="<EMAIL>",
                    password="demo123",
                    role="team_member",
                )
                db.session.add(team_user)
                print("✓ Created demo team member (username: demo, password: demo123)")

            # Create sample tasks
            sample_tasks = [
                ("Development", "Software development and coding tasks"),
                ("Testing", "Quality assurance and testing activities"),
                ("Documentation", "Writing and updating documentation"),
                ("Meetings", "Team meetings and client calls"),
                ("Research", "Research and learning activities"),
            ]

            for name, description in sample_tasks:
                task = Task.query.filter_by(name=name).first()
                if not task:
                    task = Task(name=name, description=description)
                    db.session.add(task)

            db.session.commit()
            print("✓ Created sample tasks")

        except Exception as e:
            print(f"✗ Database setup failed: {e}")
            db.session.rollback()


def setup_frontend():
    """Set up the frontend development environment."""
    print("\nSetting up frontend...")

    frontend_dir = project_root / "frontend"

    if not frontend_dir.exists():
        print("✗ Frontend directory not found")
        return

    # Install Node.js dependencies
    print("Installing Node.js dependencies...")
    run_command("npm install", cwd=frontend_dir)


def main():
    """Main setup function."""
    print("🚀 Setting up Timester development environment")
    print("=" * 50)

    # Check Python version
    if sys.version_info < (3, 12):
        print("⚠️  Python 3.12+ is recommended")

    # Check Poetry
    if not check_poetry():
        return

    # Setup backend
    setup_backend()

    # Setup frontend
    setup_frontend()

    print("\n" + "=" * 50)
    print("✅ Development setup complete!")
    print("\nNext steps:")
    print("1. Edit .env with your actual configuration values")
    print("2. Start the backend: poetry run flask run")
    print("3. Start the frontend: cd frontend && npm run dev")
    print("4. Visit http://localhost:3000")
    print("\nDefault login credentials:")
    print("  Admin: admin / admin123")
    print("  Demo user: demo / demo123")
    print("\nUseful Poetry commands:")
    print("  poetry run flask init-db       # Initialize database")
    print("  poetry run flask create-admin  # Create admin user")
    print("  poetry run pytest             # Run tests")
    print("  poetry run timester-agent     # Run desktop agent")


if __name__ == "__main__":
    main()
