#!/usr/bin/env python3
"""
Validation script to ensure the duration refactoring is working correctly.
"""
import sys
import json
from pathlib import Path

# Add the src directory to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from timester.app import create_app, db
from timester.models.user import User
from timester.models.task import Task
from timester.models.time_entry import TimeEntry
from datetime import datetime, timedelta


def test_time_entry_api_response():
    """Test that TimeEntry.to_dict() doesn't include duration_hours."""
    print("Testing TimeEntry API response format...")

    app = create_app("testing")
    with app.app_context():
        db.create_all()

        # Create test data
        user = User(username="test", email="<EMAIL>", password="test123")
        task = Task(name="Test Task", description="Test")
        db.session.add_all([user, task])
        db.session.commit()

        # Create time entry
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(hours=2, minutes=30)  # 9000 seconds

        time_entry = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        time_entry.stop_timer(end_time)
        db.session.add(time_entry)
        db.session.commit()

        # Test API response format
        response_dict = time_entry.to_dict()

        # Validate response structure
        assert (
            "duration_seconds" in response_dict
        ), "duration_seconds missing from API response"
        assert (
            "duration_hours" not in response_dict
        ), "duration_hours should not be in API response"
        assert (
            response_dict["duration_seconds"] == 9000
        ), f"Expected 9000 seconds, got {response_dict['duration_seconds']}"

        print("✓ TimeEntry API response format is correct")

        # Clean up
        db.drop_all()


def test_task_aggregation():
    """Test that Task aggregation methods use seconds."""
    print("Testing Task aggregation methods...")

    app = create_app("testing")
    with app.app_context():
        db.create_all()

        # Create test data
        user = User(username="test", email="<EMAIL>", password="test123")
        task = Task(name="Test Task", description="Test")
        db.session.add_all([user, task])
        db.session.commit()

        # Create multiple time entries
        start_time = datetime.utcnow()

        # Entry 1: 1 hour (3600 seconds)
        entry1 = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        entry1.stop_timer(start_time + timedelta(hours=1))

        # Entry 2: 30 minutes (1800 seconds)
        entry2 = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        entry2.stop_timer(start_time + timedelta(minutes=30))

        db.session.add_all([entry1, entry2])
        db.session.commit()

        # Test aggregation methods
        total_seconds = task.get_total_seconds()
        user_seconds = task.get_total_seconds_for_user(user.id)

        assert (
            total_seconds == 5400
        ), f"Expected 5400 total seconds, got {total_seconds}"
        assert user_seconds == 5400, f"Expected 5400 user seconds, got {user_seconds}"

        # Test API response format
        task_dict = task.to_dict()
        assert (
            "total_seconds" in task_dict
        ), "total_seconds missing from Task API response"
        assert (
            task_dict["total_seconds"] == 5400
        ), f"Expected 5400 in task dict, got {task_dict['total_seconds']}"

        print("✓ Task aggregation methods are correct")

        # Clean up
        db.drop_all()


def test_precision_maintenance():
    """Test that precision is maintained in seconds-based calculations."""
    print("Testing precision maintenance...")

    # Test various durations to ensure no precision loss
    test_durations = [1, 59, 60, 61, 3599, 3600, 3661, 7200, 9000, 86400]

    for seconds in test_durations:
        # Convert to hours and back
        hours = seconds / 3600.0
        back_to_seconds = int(hours * 3600)

        assert (
            back_to_seconds == seconds
        ), f"Precision lost for {seconds} seconds: {back_to_seconds}"

    print("✓ Precision is maintained in seconds-based calculations")


def test_frontend_time_utils():
    """Test that frontend time utilities work correctly."""
    print("Testing frontend time utility functions...")

    # Read the timeUtils.js file to verify it exists
    time_utils_path = project_root / "frontend" / "src" / "utils" / "timeUtils.js"

    if not time_utils_path.exists():
        print("✗ timeUtils.js file not found")
        return False

    # Read the file content
    with open(time_utils_path, "r") as f:
        content = f.read()

    # Check for required functions
    required_functions = [
        "formatDurationHMS",
        "formatDurationDecimal",
        "formatDurationHuman",
        "parseDurationToSeconds",
        "getCurrentDuration",
        "formatDuration",
    ]

    for func in required_functions:
        if func not in content:
            print(f"✗ Required function {func} not found in timeUtils.js")
            return False

    print("✓ Frontend time utilities are present")
    return True


def test_vue_component_updates():
    """Test that Vue components have been updated."""
    print("Testing Vue component updates...")

    components_to_check = [
        ("frontend/src/views/Dashboard.vue", ["formatDuration", "getCurrentDuration"]),
        (
            "frontend/src/views/TimeTracking.vue",
            ["formatDuration", "getCurrentDuration"],
        ),
        ("frontend/src/views/Admin.vue", ["formatDuration"]),
        ("frontend/src/components/DurationFormatSelector.vue", ["formatDuration"]),
    ]

    for component_path, required_imports in components_to_check:
        full_path = project_root / component_path

        if not full_path.exists():
            print(f"✗ Component {component_path} not found")
            continue

        with open(full_path, "r") as f:
            content = f.read()

        # Check for timeUtils imports
        if "timeUtils" not in content:
            print(f"✗ Component {component_path} doesn't import timeUtils")
            continue

        # Check that duration_hours is not referenced
        if "duration_hours" in content:
            print(f"⚠️  Component {component_path} still references duration_hours")

        print(f"✓ Component {component_path} updated correctly")

    return True


def main():
    """Run all validation tests."""
    print("🔍 Validating Duration Refactoring")
    print("=" * 50)

    tests = [
        ("TimeEntry API Response", test_time_entry_api_response),
        ("Task Aggregation", test_task_aggregation),
        ("Precision Maintenance", test_precision_maintenance),
        ("Frontend Time Utils", test_frontend_time_utils),
        ("Vue Component Updates", test_vue_component_updates),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            if result is not False:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            failed += 1

    print("\n" + "=" * 50)
    print(f"📊 Validation Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All validation tests passed!")
        print("\nDuration refactoring is complete and working correctly.")
        print("\nKey improvements:")
        print("- All backend calculations use seconds as primary unit")
        print("- API responses only include duration_seconds")
        print("- Frontend formatting is handled by timeUtils.js")
        print("- User preferences for duration display format")
        print("- Precision is maintained throughout the system")
    else:
        print("⚠️  Some validation tests failed. Please review the issues above.")

    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
