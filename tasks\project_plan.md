# Product Requirements Document: Simple Team Time Tracker

This document outlines the detailed requirements for a custom web application designed to track work hours and capture periodic screenshots for remote team members. The goal is to provide a focused, cost-effective tool that directly addresses our needs, leveraging a modern and efficient tech stack.

## 1. Core Objectives

* Enable team members to easily track their work hours against specific tasks.
* Periodically capture screenshots from team members' computers to show activity and provide visual context for reported hours.
* Provide administrators with a clear overview of hours worked and associated screenshots for payroll and review.
* Support a maximum of 10 team members efficiently.
* Deliver a simple, intuitive user experience for both team members and administrators.

## 2. User Roles

The application will support two primary user roles, each with distinct permissions and functionalities:

* **Team Member:**
    * Logs in to the application.
    * Starts and stops a timer for work sessions.
    * Associates active work sessions with predefined tasks.
    * Runs the lightweight desktop agent for automated screenshot capture.
    * Views their own recorded time entries.
* **Administrator:**
    * Logs in to the application.
    * Manages team member accounts (create, edit, disable).
    * Creates and manages tasks that team members can assign work to.
    * Views and reviews all time entries across the team or for specific team members/tasks.
    * Accesses and reviews captured screenshots associated with time entries.
    * Generates reports on hours worked for payroll purposes.

## 3. Features

### 3.1. User Management & Authentication

* **User Registration:** Administrators can create new Team Member accounts, specifying a username/email and initial password.
* **User Login:** Both Team Members and Administrators can log in using their registered username/email and password.
* **Password Management:** Users can reset forgotten passwords. Administrators can reset passwords for team members.
* **Role-Based Access Control:** Differentiate access and available features based on "Team Member" and "Administrator" roles.
* **Session-based Authentication:** Secure user sessions using server-side session management and `HttpOnly` cookies for enhanced security against XSS.

### 3.2. Time Tracking

* **Start/Stop Timer:** Team members can initiate and conclude a work session with a clear "Start" and "Stop" button.
* **Task Association:** Before starting a timer, team members will select a predefined task to associate their work with.
* **Time Accuracy:** The timer will accurately record elapsed time for each session.
* **Manual Time Entry:** Team Members will have the option to manually adjust or add time entries.
* **Time Entry Storage:** All time entries will be persistently stored in the database, linked to the respective team member, task, and session duration.

### 3.3. Screenshot Capture & Storage

* **Lightweight Python Desktop Agent:**
    * Runs silently in the background on team members' computers.
    * Captures a screenshot of the primary active screen at configurable intervals (e.g., every 5-10 minutes, configurable on the server-side).
    * Compresses and optimizes screenshots before upload to minimize file size.
    * Uploads captured screenshots (as binary data) via a secure API endpoint to the web application's backend.
    * Sends associated metadata with each screenshot, including:
        * User ID.
        * Timestamp of capture.
        * Optional: Basic activity level (e.g., keyboard/mouse activity detected or not, not detailed logging).
        * Optional: Title of the active window/application.
    * Handles network interruptions gracefully (e.g., retrying uploads).
* **Secure Cloud Object Storage (DigitalOcean Spaces):**
    * The backend will receive screenshot uploads and store the actual image files in a dedicated DigitalOcean Space.
    * Access to the Space will be controlled by secure API keys (DigitalOcean Spaces Access Key and Secret Key) used by the backend.
    * Screenshots will be given unique names within the Space, often including user ID and timestamp for organization (e.g., `screenshots/user_123/2025-07-11/screenshot_10-30-05_uuid.jpg`).
* **Database Metadata Storage (PostgreSQL):**
    * The PostgreSQL database will store metadata for each screenshot, including:
        * Unique ID (`id`).
        * Associated `user_id`.
        * `timestamp` of capture.
        * `gcs_object_name` (the unique name/path of the image in the DigitalOcean Space).
        * Optional: `activity_level`.
        * Optional: `active_window_title`.

### 3.4. Reporting & Review

* **Time Entry Overview:** Administrators can view a comprehensive list of all time entries, filterable by:
    * Team Member.
    * Date range.
    * Task.
* **Screenshot Review:** Administrators can review captured screenshots for individual time entries or specific team members, displayed in chronological order. The system will generate secure, time-limited signed URLs from DigitalOcean Spaces to display images, ensuring private access.
* **Activity Visualization:** Display activity levels associated with screenshots (if captured by the agent).
* **Summary Reports:** Generate aggregated reports showing:
    * Total hours worked per team member per day/week/month.
    * Total hours worked per task/project.
* **Data Export:** Allow administrators to export time entry and summary data (e.g., to CSV format) for external payroll processing.

### 3.5. Admin Dashboard

* A centralized dashboard for administrators providing a high-level overview of team activity, recent time entries, and quick links to user management, task management, and reporting features.

## 4. Technical Stack

* **Frontend (User Interface):**
    * **Framework:** Vue.js (v3)
    * **Build Tool:** Vite
    * **Styling:** Single File Components (SFCs) with `<style scoped>` blocks or traditional CSS (`.css` files imported into components/app).
    * **API Interaction:** `fetch` or `Axios` for communication with the backend.
* **Backend (Server-Side Logic & API):**
    * **Language:** Python 3
    * **Framework:** Flask or Django (to be chosen after further architectural review, but both are suitable for the requirements and session-based auth).
    * **Package Management:** Poetry
    * **Web Server Gateway Interface (WSGI):** Gunicorn
    * **External API Integration:** `google-cloud-storage` client library for DigitalOcean Spaces interaction.
* **Database:**
    * **Type:** Relational Database
    * **Implementation:** PostgreSQL (managed by DigitalOcean)
* **Object Storage (for Screenshots):**
    * **Service:** DigitalOcean Spaces (S3-compatible)
* **Desktop Agent:**
    * **Language:** Python 3
    * **Libraries:** `mss` (or similar for screen capture), `Pillow` (for image processing), `requests` (for API calls), `schedule` (for timing).
    * **Distribution:** Packaged into a standalone executable (e.g., using PyInstaller).
* **Deployment & Infrastructure:**
    * **Cloud Provider:** DigitalOcean
    * **Server:** Ubuntu LTS Droplet
    * **Reverse Proxy/Web Server:** Nginx
    * **SSL/TLS:** Let's Encrypt (via Certbot)
    * **Firewall:** UFW

## 5. Non-Functional Requirements

* **Performance:**
    * The web application shall be responsive, with page load times and API response times kept to a minimum (e.g., under 500ms for typical operations).
    * Screenshot capture and upload by the desktop agent shall be lightweight and not significantly impact the team member's system performance or network bandwidth.
    * Screenshot display in the admin panel shall load efficiently, leveraging signed URLs and potentially image optimization.
* **Security:**
    * All data in transit (web app, desktop agent API calls) shall be encrypted using HTTPS/TLS.
    * User passwords shall be securely hashed and salted before storage.
    * Session-based authentication shall leverage secure `HttpOnly` and `Secure` cookies.
    * Robust CSRF protection shall be implemented for state-changing requests.
    * Input data validation shall be performed on the backend to prevent injection attacks.
    * Least privilege principle shall be applied to GCS access keys and service accounts.
    * GCS buckets shall remain private, with access granted only via signed URLs for viewing.
* **Reliability & Availability:**
    * The application shall be designed for high uptime, with minimal single points of failure.
    * DigitalOcean's managed database service will provide automatic backups and failover capabilities for PostgreSQL.
    * Error handling and logging shall be implemented across the backend and desktop agent.
* **Scalability:**
    * The chosen architecture (separate frontend, backend, database, object storage) allows for independent scaling of components if the team size or usage significantly increases beyond 10 members.
    * The Droplet and database plans can be easily upgraded on DigitalOcean.
* **Maintainability:**
    * The codebase shall be clean, modular, and adhere to Python and Vue.js best practices.
    * Dependencies shall be managed using Poetry, ensuring reproducible environments.
    * Clear documentation for setup, deployment, and troubleshooting will be provided.
* **Cost-Effectiveness:** The chosen stack (DigitalOcean, GCS, open-source frameworks) aims to keep operational costs low while meeting core needs.

## 6. Out of Scope

* Complex project management features (e.g., Gantt charts, detailed task dependencies).
* Advanced payroll processing beyond simple hour tracking and export.
* Direct integration with external payment gateways or accounting software.
* Comprehensive, real-time keyboard/mouse activity logging beyond a simple activity indicator (to maintain privacy and simplicity).
* Complex notification systems (e.g., email alerts for inactivity).
* Mobile native applications (the focus is on a web application and a desktop agent).
* Automated screenshot analysis (e.g., AI to detect specific activities).