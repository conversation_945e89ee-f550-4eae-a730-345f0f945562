# Poetry Guide for Timester Development

This guide covers how to use Poetry for Timester development, including dependency management, virtual environments, and running commands.

## Installation

### Install Poetry

```bash
# Using the official installer (recommended)
curl -sSL https://install.python-poetry.org | python3 -

# Or using pip (if you prefer)
pip install poetry

# Verify installation
poetry --version
```

### Configure Poetry (Optional)

```bash
# Create virtual environment in project directory
poetry config virtualenvs.in-project true

# Show current configuration
poetry config --list
```

## Project Setup

### Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd timester

# Install dependencies
poetry install

# Install with development dependencies (default)
poetry install --with dev

# Install only production dependencies
poetry install --only main
```

### Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# Then initialize the database
poetry run flask init-db
poetry run flask create-admin
poetry run flask create-sample-tasks
```

## Dependency Management

### Adding Dependencies

```bash
# Add a production dependency
poetry add flask-mail

# Add a development dependency
poetry add --group dev pytest-mock

# Add with version constraints
poetry add "requests>=2.28.0,<3.0.0"

# Add from git repository
poetry add git+https://github.com/user/repo.git
```

### Removing Dependencies

```bash
# Remove a dependency
poetry remove flask-mail

# Remove a development dependency
poetry remove --group dev pytest-mock
```

### Updating Dependencies

```bash
# Update all dependencies
poetry update

# Update specific dependency
poetry update requests

# Show outdated dependencies
poetry show --outdated
```

### Viewing Dependencies

```bash
# Show all dependencies
poetry show

# Show dependency tree
poetry show --tree

# Show specific dependency info
poetry show flask
```

## Virtual Environment Management

### Activating the Environment

```bash
# Activate shell (creates new shell session)
poetry shell

# Or run commands directly
poetry run python --version
poetry run flask --help
```

### Environment Information

```bash
# Show virtual environment path
poetry env info

# Show virtual environment path only
poetry env info --path

# List all virtual environments
poetry env list
```

## Running Commands

### Flask Commands

```bash
# Run Flask development server
poetry run flask run

# Run with debug mode
poetry run flask run --debug

# Custom Flask commands
poetry run flask init-db
poetry run flask create-admin
poetry run flask create-sample-tasks

# Database migrations
poetry run flask db init
poetry run flask db migrate -m "Initial migration"
poetry run flask db upgrade
```

### Testing

```bash
# Run all tests
poetry run pytest

# Run with verbose output
poetry run pytest -v

# Run specific test file
poetry run pytest tests/test_app.py

# Run with coverage
poetry run pytest --cov=timester

# Run with coverage report
poetry run pytest --cov=timester --cov-report=html
```

### Code Quality

```bash
# Format code with Black
poetry run black src/ tests/

# Check code style with flake8
poetry run flake8 src/ tests/

# Type checking with mypy
poetry run mypy src/

# Run all quality checks
poetry run black src/ tests/ && poetry run flake8 src/ tests/
```

### Desktop Agent

```bash
# Run the desktop agent
poetry run timester-agent

# Run with configuration wizard
poetry run timester-agent --config

# Run directly with Python module
poetry run python -m timester.agent.main
```

## Development Workflow

### Daily Development

```bash
# 1. Activate environment (optional, but recommended)
poetry shell

# 2. Start backend server
flask run --debug

# 3. In another terminal, start frontend
cd frontend
npm run dev

# 4. Run tests when making changes
pytest tests/

# 5. Format code before committing
black src/ tests/
flake8 src/ tests/
```

### Using Makefile

The project includes a Makefile with Poetry commands:

```bash
# Setup development environment
make setup

# Install dependencies
make install

# Run tests
make test

# Format code
make format

# Run backend
make run-backend

# Run desktop agent
make run-agent
```

## Troubleshooting

### Common Issues

1. **Poetry not found after installation**
   ```bash
   # Add Poetry to PATH (add to ~/.bashrc or ~/.zshrc)
   export PATH="$HOME/.local/bin:$PATH"
   ```

2. **Virtual environment issues**
   ```bash
   # Remove and recreate virtual environment
   poetry env remove python
   poetry install
   ```

3. **Dependency conflicts**
   ```bash
   # Clear cache and reinstall
   poetry cache clear pypi --all
   poetry install
   ```

4. **Flask commands not working**
   ```bash
   # Ensure you're in the virtual environment
   poetry shell
   # Or use poetry run
   poetry run flask --help
   ```

### Verification

Run the verification script to check your setup:

```bash
poetry run python scripts/verify_poetry_setup.py
```

## Best Practices

1. **Always use Poetry for dependency management**
   - Don't use `pip install` directly
   - Use `poetry add` instead

2. **Use `poetry run` for commands**
   - Ensures commands run in the correct environment
   - Avoids environment activation issues

3. **Keep pyproject.toml updated**
   - Commit changes to pyproject.toml
   - Don't commit poetry.lock to version control for libraries (but do for applications)

4. **Use dependency groups**
   - Separate development and production dependencies
   - Use `--only main` for production installs

5. **Regular updates**
   - Run `poetry update` regularly
   - Check for security updates with `poetry audit` (if available)

## Integration with IDEs

### VS Code

Add to your VS Code settings:

```json
{
    "python.defaultInterpreterPath": ".venv/bin/python",
    "python.terminal.activateEnvironment": true
}
```

### PyCharm

1. Go to Settings → Project → Python Interpreter
2. Click gear icon → Add
3. Select "Poetry Environment"
4. Choose "Existing environment" and point to `.venv/bin/python`

## Resources

- [Poetry Documentation](https://python-poetry.org/docs/)
- [Poetry Commands Reference](https://python-poetry.org/docs/cli/)
- [Dependency Specification](https://python-poetry.org/docs/dependency-specification/)
