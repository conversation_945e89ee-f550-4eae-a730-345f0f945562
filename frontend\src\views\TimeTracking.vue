<template>
  <div class="time-tracking">
    <div class="card">
      <div class="card-header">
        <h2>Time Tracking</h2>
      </div>

      <!-- Current Timer Section -->
      <div class="timer-section">
        <div v-if="currentTimer" class="active-timer">
          <h3>Active Timer</h3>
          <div class="timer-display">
            <div class="timer-info">
              <p><strong>Task:</strong> {{ getTaskName(currentTimer.task_id) }}</p>
              <p><strong>Started:</strong> {{ formatTime(currentTimer.start_time) }}</p>
              <p v-if="currentTimer.description"><strong>Description:</strong> {{ currentTimer.description }}</p>
            </div>
            <div class="timer-duration">
              <span class="duration-display">{{ currentDuration }}</span>
            </div>
          </div>
          <button @click="stopTimer" class="btn btn-danger" :disabled="loading">
            Stop Timer
          </button>
        </div>

        <div v-else class="start-timer">
          <h3>Start New Timer</h3>
          <form @submit.prevent="startTimer">
            <div class="form-group">
              <label for="task">Task</label>
              <select id="task" v-model="newTimer.task_id" required :disabled="loading">
                <option value="">Select a task</option>
                <option v-for="task in tasks" :key="task.id" :value="task.id">
                  {{ task.name }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="description">Description (optional)</label>
              <textarea
                id="description"
                v-model="newTimer.description"
                rows="3"
                placeholder="What are you working on?"
                :disabled="loading"
              ></textarea>
            </div>
            <button type="submit" class="btn btn-success" :disabled="loading || !newTimer.task_id">
              {{ loading ? 'Starting...' : 'Start Timer' }}
            </button>
          </form>
        </div>
      </div>

      <!-- Manual Entry Section -->
      <div class="manual-entry-section">
        <h3>Add Manual Entry</h3>
        <form @submit.prevent="addManualEntry">
          <div class="form-row">
            <div class="form-group">
              <label for="manual-task">Task</label>
              <select id="manual-task" v-model="manualEntry.task_id" required :disabled="loading">
                <option value="">Select a task</option>
                <option v-for="task in tasks" :key="task.id" :value="task.id">
                  {{ task.name }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="manual-date">Date</label>
              <input
                id="manual-date"
                v-model="manualEntry.date"
                type="date"
                required
                :disabled="loading"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="start-time">Start Time</label>
              <input
                id="start-time"
                v-model="manualEntry.start_time"
                type="time"
                required
                :disabled="loading"
              />
            </div>
            <div class="form-group">
              <label for="end-time">End Time</label>
              <input
                id="end-time"
                v-model="manualEntry.end_time"
                type="time"
                required
                :disabled="loading"
              />
            </div>
          </div>
          <div class="form-group">
            <label for="manual-description">Description (optional)</label>
            <textarea
              id="manual-description"
              v-model="manualEntry.description"
              rows="2"
              placeholder="What did you work on?"
              :disabled="loading"
            ></textarea>
          </div>
          <button type="submit" class="btn btn-primary" :disabled="loading || !isManualEntryValid">
            {{ loading ? 'Adding...' : 'Add Entry' }}
          </button>
        </form>
      </div>

      <!-- Recent Entries -->
      <div class="recent-entries">
        <h3>Recent Entries</h3>
        <div v-if="timeEntries.length === 0" class="no-data">
          No time entries yet.
        </div>
        <div v-else class="entries-table">
          <table class="table">
            <thead>
              <tr>
                <th>Task</th>
                <th>Date</th>
                <th>Duration</th>
                <th>Description</th>
                <th>Type</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="entry in timeEntries" :key="entry.id">
                <td>{{ getTaskName(entry.task_id) }}</td>
                <td>{{ formatDate(entry.start_time) }}</td>
                <td>{{ formatDuration(entry.duration_hours) }}</td>
                <td>{{ entry.description || '-' }}</td>
                <td>
                  <span :class="entry.is_manual ? 'badge-manual' : 'badge-timer'">
                    {{ entry.is_manual ? 'Manual' : 'Timer' }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import api from '../services/api'

export default {
  name: 'TimeTracking',
  setup() {
    const currentTimer = ref(null)
    const tasks = ref([])
    const timeEntries = ref([])
    const loading = ref(false)
    const timerInterval = ref(null)

    const newTimer = ref({
      task_id: '',
      description: ''
    })

    const manualEntry = ref({
      task_id: '',
      date: new Date().toISOString().split('T')[0],
      start_time: '',
      end_time: '',
      description: ''
    })

    const currentDuration = computed(() => {
      if (!currentTimer.value) return '00:00:00'
      
      const start = new Date(currentTimer.value.start_time)
      const now = new Date()
      const diff = Math.floor((now - start) / 1000)
      
      const hours = Math.floor(diff / 3600)
      const minutes = Math.floor((diff % 3600) / 60)
      const seconds = diff % 60
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    })

    const isManualEntryValid = computed(() => {
      return manualEntry.value.task_id && 
             manualEntry.value.date && 
             manualEntry.value.start_time && 
             manualEntry.value.end_time &&
             manualEntry.value.start_time < manualEntry.value.end_time
    })

    const loadData = async () => {
      try {
        // Load tasks
        const tasksResponse = await api.get('/admin/tasks')
        tasks.value = tasksResponse.data.tasks.filter(task => task.is_active)

        // Load current timer
        const timerResponse = await api.get('/time/current')
        currentTimer.value = timerResponse.data.active_timer

        // Load recent entries
        const entriesResponse = await api.get('/time/entries?per_page=10')
        timeEntries.value = entriesResponse.data.time_entries

        // Start timer update interval if there's an active timer
        if (currentTimer.value && !timerInterval.value) {
          timerInterval.value = setInterval(() => {
            // Force reactivity update
            currentTimer.value = { ...currentTimer.value }
          }, 1000)
        }
      } catch (error) {
        console.error('Failed to load data:', error)
      }
    }

    const startTimer = async () => {
      loading.value = true
      try {
        const response = await api.post('/time/start', newTimer.value)
        currentTimer.value = response.data.time_entry
        newTimer.value = { task_id: '', description: '' }
        
        // Start timer update interval
        if (!timerInterval.value) {
          timerInterval.value = setInterval(() => {
            currentTimer.value = { ...currentTimer.value }
          }, 1000)
        }
        
        await loadData()
      } catch (error) {
        console.error('Failed to start timer:', error)
      } finally {
        loading.value = false
      }
    }

    const stopTimer = async () => {
      loading.value = true
      try {
        await api.post('/time/stop')
        currentTimer.value = null
        
        // Clear timer interval
        if (timerInterval.value) {
          clearInterval(timerInterval.value)
          timerInterval.value = null
        }
        
        await loadData()
      } catch (error) {
        console.error('Failed to stop timer:', error)
      } finally {
        loading.value = false
      }
    }

    const addManualEntry = async () => {
      loading.value = true
      try {
        const startDateTime = `${manualEntry.value.date}T${manualEntry.value.start_time}:00`
        const endDateTime = `${manualEntry.value.date}T${manualEntry.value.end_time}:00`
        
        await api.post('/time/entries', {
          task_id: manualEntry.value.task_id,
          start_time: startDateTime,
          end_time: endDateTime,
          description: manualEntry.value.description
        })
        
        manualEntry.value = {
          task_id: '',
          date: new Date().toISOString().split('T')[0],
          start_time: '',
          end_time: '',
          description: ''
        }
        
        await loadData()
      } catch (error) {
        console.error('Failed to add manual entry:', error)
      } finally {
        loading.value = false
      }
    }

    const getTaskName = (taskId) => {
      const task = tasks.value.find(t => t.id === taskId)
      return task ? task.name : 'Unknown Task'
    }

    const formatTime = (timeString) => {
      return new Date(timeString).toLocaleTimeString()
    }

    const formatDate = (timeString) => {
      return new Date(timeString).toLocaleDateString()
    }

    const formatDuration = (hours) => {
      if (!hours) return '0h 0m'
      const h = Math.floor(hours)
      const m = Math.floor((hours - h) * 60)
      return `${h}h ${m}m`
    }

    onMounted(() => {
      loadData()
    })

    onUnmounted(() => {
      if (timerInterval.value) {
        clearInterval(timerInterval.value)
      }
    })

    return {
      currentTimer,
      tasks,
      timeEntries,
      loading,
      newTimer,
      manualEntry,
      currentDuration,
      isManualEntryValid,
      startTimer,
      stopTimer,
      addManualEntry,
      getTaskName,
      formatTime,
      formatDate,
      formatDuration
    }
  }
}
</script>

<style scoped>
.timer-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.active-timer {
  background-color: #e8f5e8;
  padding: 1.5rem;
  border-radius: 8px;
}

.timer-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
}

.duration-display {
  font-size: 2rem;
  font-weight: bold;
  color: #27ae60;
  font-family: monospace;
}

.manual-entry-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.badge-manual {
  background-color: #f39c12;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.badge-timer {
  background-color: #27ae60;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}
</style>
