# Timester Environment Configuration
# Copy this file to .env and update with your actual values
#
# Setup Instructions:
# 1. Install Poetry: curl -sSL https://install.python-poetry.org | python3 -
# 2. Install dependencies: poetry install
# 3. Copy this file: cp .env.example .env
# 4. Edit this file with your actual values
# 5. Initialize database: poetry run flask init-db

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=postgresql://timester:password@localhost/timester_dev
TEST_DATABASE_URL=postgresql://timester:password@localhost/timester_test

# DigitalOcean Spaces Configuration
DO_SPACES_KEY=your-spaces-access-key
DO_SPACES_SECRET=your-spaces-secret-key
DO_SPACES_BUCKET=your-bucket-name
DO_SPACES_REGION=nyc3

# Screenshot Configuration
SCREENSHOT_INTERVAL_MINUTES=5
MAX_SCREENSHOT_SIZE_MB=5

# Security Configuration
SESSION_COOKIE_SECURE=false

# CORS Configuration (comma-separated list)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Poetry Virtual Environment (optional)
# Uncomment to force Poetry to create venv in project directory
# POETRY_VENV_IN_PROJECT=true
