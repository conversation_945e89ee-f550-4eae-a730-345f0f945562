"""
Screenshot capture functionality for the desktop agent.
"""
import io
import time
import logging
from datetime import datetime
from PIL import Image
import mss
import psutil


class ScreenshotCapture:
    """Handles screenshot capture and processing."""
    
    def __init__(self, quality=85, max_size=1920):
        """
        Initialize screenshot capture.
        
        Args:
            quality: JPEG quality (1-100)
            max_size: Maximum width/height in pixels
        """
        self.quality = quality
        self.max_size = max_size
        self.logger = logging.getLogger(__name__)
        
        # Initialize screen capture
        self.sct = mss.mss()
    
    def capture(self):
        """
        Capture a screenshot and return processed image data.
        
        Returns:
            dict: Screenshot data including image bytes, timestamp, and metadata
        """
        try:
            # Get the primary monitor
            monitor = self.sct.monitors[1]  # Monitor 1 is usually the primary
            
            # Capture screenshot
            screenshot = self.sct.grab(monitor)
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            # Resize if necessary
            if max(img.size) > self.max_size:
                img = self._resize_image(img)
            
            # Convert to JPEG bytes
            img_bytes = self._image_to_bytes(img)
            
            # Get metadata
            timestamp = datetime.utcnow()
            activity_level = self._detect_activity_level()
            active_window_title = self._get_active_window_title()
            
            return {
                'image_data': img_bytes,
                'timestamp': timestamp,
                'activity_level': activity_level,
                'active_window_title': active_window_title,
                'file_size': len(img_bytes)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {e}")
            return None
    
    def _resize_image(self, img):
        """Resize image while maintaining aspect ratio."""
        width, height = img.size
        
        if width > height:
            new_width = self.max_size
            new_height = int((height * self.max_size) / width)
        else:
            new_height = self.max_size
            new_width = int((width * self.max_size) / height)
        
        return img.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    def _image_to_bytes(self, img):
        """Convert PIL Image to JPEG bytes."""
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='JPEG', quality=self.quality, optimize=True)
        return img_buffer.getvalue()
    
    def _detect_activity_level(self):
        """
        Detect basic activity level based on CPU usage.
        
        Returns:
            str: 'active', 'idle', or 'unknown'
        """
        try:
            # Simple activity detection based on CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            if cpu_percent > 20:
                return 'active'
            elif cpu_percent < 5:
                return 'idle'
            else:
                return 'active'  # Default to active for moderate usage
                
        except Exception as e:
            self.logger.warning(f"Failed to detect activity level: {e}")
            return 'unknown'
    
    def _get_active_window_title(self):
        """
        Get the title of the currently active window.
        
        Returns:
            str: Window title or None if unable to detect
        """
        try:
            import platform
            
            if platform.system() == "Windows":
                return self._get_active_window_title_windows()
            elif platform.system() == "Darwin":  # macOS
                return self._get_active_window_title_macos()
            elif platform.system() == "Linux":
                return self._get_active_window_title_linux()
            else:
                return None
                
        except Exception as e:
            self.logger.warning(f"Failed to get active window title: {e}")
            return None
    
    def _get_active_window_title_windows(self):
        """Get active window title on Windows."""
        try:
            import win32gui
            
            hwnd = win32gui.GetForegroundWindow()
            window_title = win32gui.GetWindowText(hwnd)
            return window_title if window_title else None
            
        except ImportError:
            self.logger.warning("pywin32 not available for Windows window detection")
            return None
        except Exception as e:
            self.logger.warning(f"Failed to get Windows window title: {e}")
            return None
    
    def _get_active_window_title_macos(self):
        """Get active window title on macOS."""
        try:
            from AppKit import NSWorkspace
            
            active_app = NSWorkspace.sharedWorkspace().activeApplication()
            if active_app:
                return active_app.get('NSApplicationName')
            return None
            
        except ImportError:
            self.logger.warning("PyObjC not available for macOS window detection")
            return None
        except Exception as e:
            self.logger.warning(f"Failed to get macOS window title: {e}")
            return None
    
    def _get_active_window_title_linux(self):
        """Get active window title on Linux."""
        try:
            import subprocess
            
            # Try using xdotool
            result = subprocess.run(
                ['xdotool', 'getwindowfocus', 'getwindowname'],
                capture_output=True,
                text=True,
                timeout=2
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            
            return None
            
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.logger.warning("xdotool not available for Linux window detection")
            return None
        except Exception as e:
            self.logger.warning(f"Failed to get Linux window title: {e}")
            return None
