"""
Authentication utilities and decorators.
"""
from functools import wraps
from flask import session, jsonify


def login_required(f):
    """Decorator to require user authentication."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function


def admin_required(f):
    """Decorator to require administrator role."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        
        if session.get('user_role') != 'administrator':
            return jsonify({'error': 'Administrator access required'}), 403
        
        return f(*args, **kwargs)
    return decorated_function


def get_current_user_id():
    """Get the current user ID from session."""
    return session.get('user_id')


def get_current_user_role():
    """Get the current user role from session."""
    return session.get('user_role')


def is_authenticated():
    """Check if user is authenticated."""
    return 'user_id' in session


def is_admin():
    """Check if current user is an administrator."""
    return session.get('user_role') == 'administrator'
