"""
Basic tests for the Timester application.

Run with: poetry run pytest
"""

import pytest
from timester.app import create_app, db
from timester.models.user import User
from timester.models.task import Task
from timester.models.time_entry import TimeEntry
from datetime import datetime, timedelta


@pytest.fixture
def app():
    """Create and configure a test app."""
    app = create_app("testing")

    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create a test client."""
    return app.test_client()


@pytest.fixture
def admin_user(app):
    """Create an admin user for testing."""
    with app.app_context():
        admin = User(
            username="admin",
            email="<EMAIL>",
            password="password123",
            role="administrator",
        )
        db.session.add(admin)
        db.session.commit()
        return admin


@pytest.fixture
def team_user(app):
    """Create a team member user for testing."""
    with app.app_context():
        user = User(
            username="teamuser",
            email="<EMAIL>",
            password="password123",
            role="team_member",
        )
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def sample_task(app):
    """Create a sample task for testing."""
    with app.app_context():
        task = Task(name="Development", description="Software development tasks")
        db.session.add(task)
        db.session.commit()
        return task


def test_app_creation(app):
    """Test that the app is created successfully."""
    assert app is not None
    assert app.config["TESTING"] is True


def test_user_model(app, admin_user):
    """Test user model functionality."""
    with app.app_context():
        user = User.query.filter_by(username="admin").first()
        assert user is not None
        assert user.username == "admin"
        assert user.email == "<EMAIL>"
        assert user.role == "administrator"
        assert user.is_admin() is True
        assert user.check_password("password123") is True
        assert user.check_password("wrongpassword") is False


def test_task_model(app, sample_task):
    """Test task model functionality."""
    with app.app_context():
        task = Task.query.filter_by(name="Development").first()
        assert task is not None
        assert task.name == "Development"
        assert task.description == "Software development tasks"
        assert task.is_active is True
        assert task.get_total_seconds() == 0


def test_login_endpoint(client, admin_user):
    """Test login endpoint."""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "password123"}
    )

    assert response.status_code == 200
    data = response.get_json()
    assert data["message"] == "Login successful"
    assert data["user"]["username"] == "admin"


def test_login_invalid_credentials(client, admin_user):
    """Test login with invalid credentials."""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "wrongpassword"}
    )

    assert response.status_code == 401
    data = response.get_json()
    assert data["error"] == "Invalid credentials"


def test_protected_endpoint_without_auth(client):
    """Test accessing protected endpoint without authentication."""
    response = client.get("/api/auth/me")
    assert response.status_code == 401


def test_admin_endpoint_access(client, admin_user):
    """Test admin endpoint access."""
    # Login first
    client.post(
        "/api/auth/login", json={"username": "admin", "password": "password123"}
    )

    # Access admin endpoint
    response = client.get("/api/admin/users")
    assert response.status_code == 200


def test_team_member_admin_access_denied(client, team_user):
    """Test that team members cannot access admin endpoints."""
    # Login as team member
    client.post(
        "/api/auth/login", json={"username": "teamuser", "password": "password123"}
    )

    # Try to access admin endpoint
    response = client.get("/api/admin/users")
    assert response.status_code == 403


def test_time_entry_duration_seconds(app):
    """Test that time entries use duration_seconds correctly."""
    with app.app_context():
        # Create test data within the app context
        user = User(username="testuser", email="<EMAIL>", password="test123")
        task = Task(name="Test Task", description="Test")
        db.session.add_all([user, task])
        db.session.commit()

        # Create a time entry with specific duration
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(
            hours=2, minutes=30
        )  # 2.5 hours = 9000 seconds

        time_entry = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        time_entry.stop_timer(end_time)

        db.session.add(time_entry)
        db.session.commit()

        # Test duration calculations
        assert time_entry.duration_seconds == 9000

        # Test to_dict doesn't include duration_hours
        entry_dict = time_entry.to_dict()
        assert "duration_seconds" in entry_dict
        assert "duration_hours" not in entry_dict
        assert entry_dict["duration_seconds"] == 9000


def test_task_total_seconds(app):
    """Test that task total calculations use seconds."""
    with app.app_context():
        # Create test data within the app context
        user = User(username="testuser2", email="<EMAIL>", password="test123")
        task = Task(name="Test Task 2", description="Test")
        db.session.add_all([user, task])
        db.session.commit()

        # Create multiple time entries
        start_time = datetime.utcnow()

        # Entry 1: 1 hour (3600 seconds)
        entry1 = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        entry1.stop_timer(start_time + timedelta(hours=1))

        # Entry 2: 30 minutes (1800 seconds)
        entry2 = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        entry2.stop_timer(start_time + timedelta(minutes=30))

        db.session.add_all([entry1, entry2])
        db.session.commit()

        # Test total calculations
        assert task.get_total_seconds() == 5400  # 3600 + 1800
        assert task.get_total_seconds_for_user(user.id) == 5400

        # Test to_dict uses total_seconds
        task_dict = task.to_dict()
        assert "total_seconds" in task_dict
        assert task_dict["total_seconds"] == 5400
