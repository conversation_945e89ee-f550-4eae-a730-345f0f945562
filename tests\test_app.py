"""
Basic tests for the Timester application.

Run with: poetry run pytest
"""

import pytest
from timester.app import create_app, db
from timester.models.user import User
from timester.models.task import Task


@pytest.fixture
def app():
    """Create and configure a test app."""
    app = create_app("testing")

    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create a test client."""
    return app.test_client()


@pytest.fixture
def admin_user(app):
    """Create an admin user for testing."""
    with app.app_context():
        admin = User(
            username="admin",
            email="<EMAIL>",
            password="password123",
            role="administrator",
        )
        db.session.add(admin)
        db.session.commit()
        return admin


@pytest.fixture
def team_user(app):
    """Create a team member user for testing."""
    with app.app_context():
        user = User(
            username="teamuser",
            email="<EMAIL>",
            password="password123",
            role="team_member",
        )
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def sample_task(app):
    """Create a sample task for testing."""
    with app.app_context():
        task = Task(name="Development", description="Software development tasks")
        db.session.add(task)
        db.session.commit()
        return task


def test_app_creation(app):
    """Test that the app is created successfully."""
    assert app is not None
    assert app.config["TESTING"] is True


def test_user_model(app, admin_user):
    """Test user model functionality."""
    with app.app_context():
        user = User.query.filter_by(username="admin").first()
        assert user is not None
        assert user.username == "admin"
        assert user.email == "<EMAIL>"
        assert user.role == "administrator"
        assert user.is_admin() is True
        assert user.check_password("password123") is True
        assert user.check_password("wrongpassword") is False


def test_task_model(app, sample_task):
    """Test task model functionality."""
    with app.app_context():
        task = Task.query.filter_by(name="Development").first()
        assert task is not None
        assert task.name == "Development"
        assert task.description == "Software development tasks"
        assert task.is_active is True
        assert task.get_total_hours() == 0.0


def test_login_endpoint(client, admin_user):
    """Test login endpoint."""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "password123"}
    )

    assert response.status_code == 200
    data = response.get_json()
    assert data["message"] == "Login successful"
    assert data["user"]["username"] == "admin"


def test_login_invalid_credentials(client, admin_user):
    """Test login with invalid credentials."""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "wrongpassword"}
    )

    assert response.status_code == 401
    data = response.get_json()
    assert data["error"] == "Invalid credentials"


def test_protected_endpoint_without_auth(client):
    """Test accessing protected endpoint without authentication."""
    response = client.get("/api/auth/me")
    assert response.status_code == 401


def test_admin_endpoint_access(client, admin_user):
    """Test admin endpoint access."""
    # Login first
    client.post(
        "/api/auth/login", json={"username": "admin", "password": "password123"}
    )

    # Access admin endpoint
    response = client.get("/api/admin/users")
    assert response.status_code == 200


def test_team_member_admin_access_denied(client, team_user):
    """Test that team members cannot access admin endpoints."""
    # Login as team member
    client.post(
        "/api/auth/login", json={"username": "teamuser", "password": "password123"}
    )

    # Try to access admin endpoint
    response = client.get("/api/admin/users")
    assert response.status_code == 403
