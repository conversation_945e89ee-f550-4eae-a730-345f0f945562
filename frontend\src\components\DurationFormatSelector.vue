<template>
  <div class="duration-format-selector">
    <label for="duration-format">Duration Display Format:</label>
    <select 
      id="duration-format" 
      v-model="selectedFormat" 
      @change="updateFormat"
      class="format-select"
    >
      <option value="hms">HH:MM:SS (e.g., 02:30:45)</option>
      <option value="decimal">Decimal Hours (e.g., 2.51)</option>
      <option value="human">Human Readable (e.g., 2h 30m)</option>
    </select>
    
    <div class="format-preview">
      <span class="preview-label">Preview:</span>
      <span class="preview-value">{{ formatPreview }}</span>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { 
  formatDuration, 
  getDurationDisplayPreference, 
  setDurationDisplayPreference 
} from '../utils/timeUtils'

export default {
  name: 'DurationFormatSelector',
  emits: ['format-changed'],
  setup(props, { emit }) {
    const selectedFormat = ref('hms')
    const previewSeconds = 9045 // 2 hours, 30 minutes, 45 seconds

    const formatPreview = computed(() => {
      return formatDuration(previewSeconds, selectedFormat.value)
    })

    const updateFormat = () => {
      setDurationDisplayPreference(selectedFormat.value)
      emit('format-changed', selectedFormat.value)
    }

    onMounted(() => {
      selectedFormat.value = getDurationDisplayPreference()
    })

    return {
      selectedFormat,
      formatPreview,
      updateFormat
    }
  }
}
</script>

<style scoped>
.duration-format-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.duration-format-selector label {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.format-select {
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9rem;
}

.format-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #e8f4f8;
  border-radius: 4px;
}

.preview-label {
  font-weight: 500;
  color: #666;
}

.preview-value {
  font-family: monospace;
  font-weight: bold;
  color: #2c3e50;
}
</style>
