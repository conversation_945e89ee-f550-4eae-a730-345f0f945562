# Timester - Team Time Tracking Application

A custom web application designed to track work hours and capture periodic screenshots for remote team members. Built with Flask (Python) backend, Vue.js frontend, and a Python desktop agent for screenshot capture.

## Features

- **Time Tracking**: Start/stop timers and manual time entry
- **Screenshot Capture**: Automated screenshot capture via desktop agent
- **User Management**: Role-based access control (Team Members & Administrators)
- **Task Management**: Create and manage work tasks
- **Reporting**: View time entries and generate reports
- **Cloud Storage**: Screenshots stored in DigitalOcean Spaces

## Architecture

- **Backend**: Flask with SQLAlchemy, PostgreSQL database
- **Frontend**: Vue.js 3 with Vite build tool
- **Desktop Agent**: Python application for screenshot capture
- **Storage**: DigitalOcean Spaces for screenshot storage
- **Authentication**: Session-based authentication with secure cookies

## Project Structure

```
timester/
├── src/timester/           # Backend Flask application
│   ├── models/            # Database models
│   ├── routes/            # API route blueprints
│   ├── services/          # Business logic services
│   ├── utils/             # Utility functions
│   ├── agent/             # Desktop agent code
│   ├── app.py             # Flask application factory
│   ├── config.py          # Configuration settings
│   └── cli.py             # CLI commands
├── frontend/              # Vue.js frontend application
│   ├── src/
│   │   ├── components/    # Vue components
│   │   ├── views/         # Page views
│   │   ├── stores/        # Pinia stores
│   │   ├── services/      # API services
│   │   └── router/        # Vue Router configuration
│   ├── package.json       # Frontend dependencies
│   └── vite.config.js     # Vite configuration
├── tests/                 # Test files
├── docs/                  # Documentation
├── pyproject.toml         # Python project configuration
└── README.md              # This file
```

## Quick Start

### Prerequisites

- Python 3.12+
- Node.js 18+
- PostgreSQL
- DigitalOcean Spaces account (for screenshot storage)

### Backend Setup

1. **Install dependencies**:
   ```bash
   pip install -e .
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Initialize database**:
   ```bash
   flask init-db
   flask create-admin
   flask create-sample-tasks
   ```

4. **Run the backend**:
   ```bash
   flask run
   ```

### Frontend Setup

1. **Install dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

### Desktop Agent Setup

1. **Configure the agent**:
   ```bash
   python -m timester.agent.main --config
   ```

2. **Run the agent**:
   ```bash
   python -m timester.agent.main
   ```

## Configuration

### Environment Variables

Key environment variables (see `.env.example`):

- `DATABASE_URL`: PostgreSQL connection string
- `SECRET_KEY`: Flask secret key for sessions
- `DO_SPACES_KEY`: DigitalOcean Spaces access key
- `DO_SPACES_SECRET`: DigitalOcean Spaces secret key
- `DO_SPACES_BUCKET`: DigitalOcean Spaces bucket name
- `SCREENSHOT_INTERVAL_MINUTES`: Screenshot capture interval

### Database Setup

The application uses PostgreSQL. Create a database and user:

```sql
CREATE DATABASE timester_dev;
CREATE USER timester WITH PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE timester_dev TO timester;
```

### DigitalOcean Spaces

1. Create a DigitalOcean Spaces bucket
2. Generate API keys with read/write access
3. Configure the environment variables

## Development

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black src/
```

### Frontend Development

```bash
cd frontend
npm run dev    # Development server
npm run build  # Production build
npm run lint   # Linting
```

## Deployment

### Production Setup

1. **Configure production environment**:
   - Set `FLASK_ENV=production`
   - Use strong `SECRET_KEY`
   - Configure SSL/TLS
   - Set up reverse proxy (Nginx)

2. **Database migrations**:
   ```bash
   flask db upgrade
   ```

3. **Build frontend**:
   ```bash
   cd frontend
   npm run build
   ```

4. **Run with Gunicorn**:
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 "timester.app:create_app()"
   ```

## API Documentation

### Authentication Endpoints

- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/change-password` - Change password

### Time Tracking Endpoints

- `POST /api/time/start` - Start timer
- `POST /api/time/stop` - Stop timer
- `GET /api/time/current` - Get current timer
- `GET /api/time/entries` - Get time entries
- `POST /api/time/entries` - Create manual entry

### Admin Endpoints

- `GET /api/admin/users` - List users
- `POST /api/admin/users` - Create user
- `PUT /api/admin/users/:id` - Update user
- `GET /api/admin/tasks` - List tasks
- `POST /api/admin/tasks` - Create task
- `GET /api/admin/time-entries` - Get all time entries

### Screenshot Endpoints

- `POST /api/screenshots/upload` - Upload screenshot
- `GET /api/screenshots/` - List screenshots (admin)
- `GET /api/screenshots/:id/url` - Get signed URL (admin)

## Security Considerations

- Session-based authentication with secure cookies
- CSRF protection on state-changing requests
- Input validation and sanitization
- Private screenshot storage with signed URLs
- Password hashing with bcrypt
- HTTPS enforcement in production

## License

AGPL License - see LICENSE file for details.

## Support

For issues and questions, please use the GitHub issue tracker.