"""
Configuration settings for the Timester application.
"""
import os
from datetime import timedelta


class BaseConfig:
    """Base configuration class."""
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SECURE = os.getenv('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # DigitalOcean Spaces configuration
    DO_SPACES_KEY = os.getenv('DO_SPACES_KEY')
    DO_SPACES_SECRET = os.getenv('DO_SPACES_SECRET')
    DO_SPACES_BUCKET = os.getenv('DO_SPACES_BUCKET')
    DO_SPACES_REGION = os.getenv('DO_SPACES_REGION', 'nyc3')
    DO_SPACES_ENDPOINT = f"https://{DO_SPACES_REGION}.digitaloceanspaces.com"
    
    # Screenshot configuration
    SCREENSHOT_INTERVAL_MINUTES = int(os.getenv('SCREENSHOT_INTERVAL_MINUTES', '5'))
    MAX_SCREENSHOT_SIZE_MB = int(os.getenv('MAX_SCREENSHOT_SIZE_MB', '5'))
    
    # CORS configuration
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'http://localhost:3000').split(',')


class DevelopmentConfig(BaseConfig):
    """Development configuration."""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.getenv(
        'DATABASE_URL',
        'postgresql://timester:password@localhost/timester_dev'
    )


class TestingConfig(BaseConfig):
    """Testing configuration."""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.getenv(
        'TEST_DATABASE_URL',
        'postgresql://timester:password@localhost/timester_test'
    )
    WTF_CSRF_ENABLED = False


class ProductionConfig(BaseConfig):
    """Production configuration."""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL')
    SESSION_COOKIE_SECURE = True
    
    # Additional production security settings
    PREFERRED_URL_SCHEME = 'https'


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
