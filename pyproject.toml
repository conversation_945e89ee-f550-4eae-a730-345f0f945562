[project]
name = "timester"
version = "0.1.0"
description = "A custom web application to track work hours and capture periodic screenshots for remote team members"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
license = {text = "AGPL"}
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "flask>=3.0.0",
    "flask-sqlalchemy>=3.1.0",
    "flask-migrate>=4.0.0",
    "flask-cors>=4.0.0",
    "psycopg2-binary>=2.9.0",
    "boto3>=1.34.0",
    "pillow>=10.0.0",
    "python-dotenv>=1.0.0",
    "werkzeug>=3.0.0",
    "bcrypt>=4.0.0",
    "requests>=2.31.0",
    "gunicorn>=21.0.0",
]

[tool.poetry]
name = "timester"
version = "0.1.0"
description = "A custom web application to track work hours and capture periodic screenshots for remote team members"
authors = ["<PERSON> <<EMAIL>>"]
license = "AGPL"
readme = "README.md"
packages = [{include = "timester", from = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
flask = "^3.0.0"
flask-sqlalchemy = "^3.1.0"
flask-migrate = "^4.0.0"
flask-cors = "^4.0.0"
psycopg2-binary = "^2.9.0"
boto3 = "^1.34.0"
pillow = "^10.0.0"
python-dotenv = "^1.0.0"
werkzeug = "^3.0.0"
bcrypt = "^4.0.0"
requests = "^2.31.0"
gunicorn = "^21.0.0"
mss = "^9.0.0"
psutil = "^5.9.0"
click = "^8.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-flask = "^1.3.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
