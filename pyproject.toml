[tool.poetry]
name = "timester"
version = "0.1.0"
description = "A custom web application to track work hours and capture periodic screenshots for remote team members"
authors = ["<PERSON> <<EMAIL>>"]
license = "AGPL-3.0"
readme = "README.md"
packages = [{include = "timester", from = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
flask = "^3.0.0"
flask-sqlalchemy = "^3.1.0"
flask-migrate = "^4.0.0"
flask-cors = "^4.0.0"
psycopg2-binary = "^2.9.0"
boto3 = "^1.34.0"
pillow = "^10.0.0"
python-dotenv = "^1.0.0"
werkzeug = "^3.0.0"
bcrypt = "^4.0.0"
requests = "^2.31.0"
gunicorn = "^21.0.0"
mss = "^9.0.0"
psutil = "^5.9.0"
click = "^8.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-flask = "^1.3.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"
pytest-cov = "^4.1.0"

[tool.poetry.scripts]
timester-agent = "timester.agent.main:main"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
