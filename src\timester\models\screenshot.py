"""
Screenshot model for storing screenshot metadata.
"""
from datetime import datetime
from timester.app import db


class Screenshot(db.Model):
    """Screenshot model for storing screenshot metadata."""
    
    __tablename__ = 'screenshots'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False)
    spaces_object_name = db.Column(db.String(500), nullable=False)
    file_size_bytes = db.Column(db.Integer)
    activity_level = db.Column(db.String(20))  # 'active', 'idle', 'unknown'
    active_window_title = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_screenshots_user_timestamp', 'user_id', 'timestamp'),
        db.Index('idx_screenshots_timestamp', 'timestamp'),
    )
    
    def __init__(self, user_id, timestamp, spaces_object_name, 
                 file_size_bytes=None, activity_level=None, active_window_title=None):
        """Initialize a new screenshot record."""
        self.user_id = user_id
        self.timestamp = timestamp
        self.spaces_object_name = spaces_object_name
        self.file_size_bytes = file_size_bytes
        self.activity_level = activity_level
        self.active_window_title = active_window_title
    
    def get_file_size_mb(self):
        """Get file size in megabytes."""
        if self.file_size_bytes is None:
            return None
        return self.file_size_bytes / (1024 * 1024)
    
    def to_dict(self):
        """Convert screenshot to dictionary representation."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'timestamp': self.timestamp.isoformat(),
            'spaces_object_name': self.spaces_object_name,
            'file_size_bytes': self.file_size_bytes,
            'file_size_mb': self.get_file_size_mb(),
            'activity_level': self.activity_level,
            'active_window_title': self.active_window_title,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        """String representation of the screenshot."""
        return f'<Screenshot {self.id} for user {self.user_id}>'
