   """
   Project model for organizing related tasks.
   """
   from datetime import datetime
   from timester.app import db
   
   
   class Project(db.Model):
       """Project model for organizing related tasks."""
       
       __tablename__ = 'projects'
       
       id = db.<PERSON>umn(db.Integer, primary_key=True)
       name = db.<PERSON>umn(db.String(200), nullable=False)
       description = db.<PERSON>umn(db.Text)
       is_active = db.Column(db.<PERSON>, default=True, nullable=False)
       created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
       updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                             onupdate=datetime.utcnow, nullable=False)
       
       # Relationships
       tasks = db.relationship('Task', backref='project', lazy='dynamic',
                              cascade='all, delete-orphan')
       
       def __init__(self, name, description=None):
           """Initialize a new project."""
           self.name = name
           self.description = description