"""
Configuration management for the desktop agent.
"""
import os
import json
from pathlib import Path


class AgentConfig:
    """Configuration manager for the desktop agent."""
    
    def __init__(self, config_path=None):
        """Initialize configuration."""
        self.config_path = config_path or self._get_default_config_path()
        self.config_data = {}
        
        # Default configuration values
        self.defaults = {
            'server_url': 'http://localhost:5000',
            'username': '',
            'password': '',
            'capture_interval': 300,  # 5 minutes in seconds
            'image_quality': 85,
            'max_image_size': 1920,  # Max width/height in pixels
            'retry_attempts': 3,
            'retry_delay': 5,  # seconds
            'log_level': 'INFO'
        }
        
        self._load_config()
    
    def _get_default_config_path(self):
        """Get the default configuration file path."""
        config_dir = Path.home() / '.timester'
        config_dir.mkdir(exist_ok=True)
        return config_dir / 'agent_config.json'
    
    def _load_config(self):
        """Load configuration from file."""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    self.config_data = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading config file: {e}")
                self.config_data = {}
        else:
            # Create default config file
            self.save_config()
    
    def save_config(self):
        """Save current configuration to file."""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config_data, f, indent=2)
        except IOError as e:
            print(f"Error saving config file: {e}")
    
    def get(self, key, default=None):
        """Get a configuration value."""
        return self.config_data.get(key, self.defaults.get(key, default))
    
    def set(self, key, value):
        """Set a configuration value."""
        self.config_data[key] = value
    
    @property
    def server_url(self):
        """Get the server URL."""
        return self.get('server_url')
    
    @property
    def username(self):
        """Get the username."""
        return self.get('username')
    
    @property
    def password(self):
        """Get the password."""
        return self.get('password')
    
    @property
    def capture_interval(self):
        """Get the capture interval in seconds."""
        return self.get('capture_interval')
    
    @property
    def image_quality(self):
        """Get the image quality (1-100)."""
        return self.get('image_quality')
    
    @property
    def max_image_size(self):
        """Get the maximum image size."""
        return self.get('max_image_size')
    
    @property
    def retry_attempts(self):
        """Get the number of retry attempts."""
        return self.get('retry_attempts')
    
    @property
    def retry_delay(self):
        """Get the retry delay in seconds."""
        return self.get('retry_delay')
    
    @property
    def log_level(self):
        """Get the log level."""
        return self.get('log_level')
    
    def is_configured(self):
        """Check if the agent is properly configured."""
        return bool(self.username and self.password and self.server_url)
    
    def setup_wizard(self):
        """Interactive setup wizard for first-time configuration."""
        print("Timester Agent Configuration Setup")
        print("=" * 40)
        
        # Server URL
        server_url = input(f"Server URL [{self.server_url}]: ").strip()
        if server_url:
            self.set('server_url', server_url)
        
        # Username
        username = input(f"Username [{self.username}]: ").strip()
        if username:
            self.set('username', username)
        
        # Password
        import getpass
        password = getpass.getpass("Password: ").strip()
        if password:
            self.set('password', password)
        
        # Capture interval
        interval_input = input(f"Capture interval in minutes [{self.capture_interval // 60}]: ").strip()
        if interval_input:
            try:
                interval_minutes = int(interval_input)
                self.set('capture_interval', interval_minutes * 60)
            except ValueError:
                print("Invalid interval, using default")
        
        # Save configuration
        self.save_config()
        print("\nConfiguration saved successfully!")
        
        return self.is_configured()
