"""
API client for communicating with the Timester server.
"""
import io
import time
import logging
import requests
from datetime import datetime


class ApiClient:
    """Client for communicating with the Timester API."""
    
    def __init__(self, server_url, username, password):
        """
        Initialize the API client.
        
        Args:
            server_url: Base URL of the Timester server
            username: <PERSON><PERSON><PERSON> for authentication
            password: Password for authentication
        """
        self.server_url = server_url.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.logger = logging.getLogger(__name__)
        
        # Set default timeout
        self.session.timeout = 30
    
    def authenticate(self):
        """
        Authenticate with the server and establish a session.
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            response = self.session.post(
                f"{self.server_url}/api/auth/login",
                json={
                    'username': self.username,
                    'password': self.password
                }
            )
            
            if response.status_code == 200:
                self.logger.info("Authentication successful")
                return True
            else:
                self.logger.error(f"Authentication failed: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Authentication request failed: {e}")
            return False
    
    def upload_screenshot(self, image_data, timestamp, activity_level=None, active_window_title=None):
        """
        Upload a screenshot to the server.
        
        Args:
            image_data: Binary image data
            timestamp: Timestamp when screenshot was taken
            activity_level: Activity level ('active', 'idle', 'unknown')
            active_window_title: Title of the active window
        
        Returns:
            bool: True if upload successful, False otherwise
        """
        try:
            # Prepare form data
            files = {
                'screenshot': ('screenshot.jpg', io.BytesIO(image_data), 'image/jpeg')
            }
            
            data = {
                'timestamp': timestamp.isoformat() + 'Z'
            }
            
            if activity_level:
                data['activity_level'] = activity_level
            
            if active_window_title:
                data['active_window_title'] = active_window_title[:500]  # Truncate if too long
            
            # Upload with retry logic
            for attempt in range(3):
                try:
                    response = self.session.post(
                        f"{self.server_url}/api/screenshots/upload",
                        files=files,
                        data=data
                    )
                    
                    if response.status_code == 201:
                        self.logger.debug("Screenshot uploaded successfully")
                        return True
                    elif response.status_code == 401:
                        # Re-authenticate and retry
                        self.logger.warning("Authentication expired, re-authenticating")
                        if self.authenticate():
                            continue
                        else:
                            return False
                    else:
                        self.logger.warning(f"Upload failed: {response.status_code} - {response.text}")
                        return False
                        
                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"Upload attempt {attempt + 1} failed: {e}")
                    if attempt < 2:  # Don't sleep on last attempt
                        time.sleep(5)
                    continue
            
            self.logger.error("All upload attempts failed")
            return False
            
        except Exception as e:
            self.logger.error(f"Unexpected error during upload: {e}")
            return False
    
    def get_server_config(self):
        """
        Get configuration from the server.
        
        Returns:
            dict: Server configuration or None if failed
        """
        try:
            response = self.session.get(f"{self.server_url}/api/config")
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.warning(f"Failed to get server config: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.warning(f"Failed to get server config: {e}")
            return None
    
    def check_connection(self):
        """
        Check if the server is reachable.
        
        Returns:
            bool: True if server is reachable, False otherwise
        """
        try:
            response = self.session.get(f"{self.server_url}/api/health", timeout=10)
            return response.status_code == 200
            
        except requests.exceptions.RequestException:
            return False
    
    def logout(self):
        """Logout and clear the session."""
        try:
            self.session.post(f"{self.server_url}/api/auth/logout")
        except requests.exceptions.RequestException:
            pass  # Ignore errors during logout
        
        self.session.close()
        self.logger.info("Logged out and session closed")
