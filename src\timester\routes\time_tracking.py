"""
Time tracking routes for starting/stopping timers and managing time entries.
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, session
from sqlalchemy import and_, or_
from timester.app import db
from timester.models.user import User
from timester.models.task import Task
from timester.models.time_entry import TimeEntry
from timester.utils.auth import login_required, admin_required

time_bp = Blueprint('time', __name__)


@time_bp.route('/start', methods=['POST'])
@login_required
def start_timer():
    """Start a new time tracking session."""
    data = request.get_json()
    
    if not data or not data.get('task_id'):
        return jsonify({'error': 'Task ID is required'}), 400
    
    user_id = session['user_id']
    
    # Check if user has an active timer
    active_entry = TimeEntry.query.filter_by(
        user_id=user_id,
        end_time=None
    ).first()
    
    if active_entry:
        return jsonify({'error': 'You already have an active timer running'}), 400
    
    # Verify task exists and is active
    task = Task.query.filter_by(id=data['task_id'], is_active=True).first()
    if not task:
        return jsonify({'error': 'Task not found or inactive'}), 404
    
    # Create new time entry
    time_entry = TimeEntry(
        user_id=user_id,
        task_id=data['task_id'],
        description=data.get('description')
    )
    
    db.session.add(time_entry)
    db.session.commit()
    
    return jsonify({
        'message': 'Timer started successfully',
        'time_entry': time_entry.to_dict()
    }), 201


@time_bp.route('/stop', methods=['POST'])
@login_required
def stop_timer():
    """Stop the current active timer."""
    user_id = session['user_id']
    
    # Find active timer
    active_entry = TimeEntry.query.filter_by(
        user_id=user_id,
        end_time=None
    ).first()
    
    if not active_entry:
        return jsonify({'error': 'No active timer found'}), 404
    
    # Stop the timer
    active_entry.stop_timer()
    db.session.commit()
    
    return jsonify({
        'message': 'Timer stopped successfully',
        'time_entry': active_entry.to_dict()
    }), 200


@time_bp.route('/current', methods=['GET'])
@login_required
def get_current_timer():
    """Get current active timer for the user."""
    user_id = session['user_id']
    
    active_entry = TimeEntry.query.filter_by(
        user_id=user_id,
        end_time=None
    ).first()
    
    if not active_entry:
        return jsonify({'active_timer': None}), 200
    
    return jsonify({'active_timer': active_entry.to_dict()}), 200


@time_bp.route('/entries', methods=['GET'])
@login_required
def get_time_entries():
    """Get time entries for the current user."""
    user_id = session['user_id']
    
    # Parse query parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    task_id = request.args.get('task_id')
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 100)
    
    # Build query
    query = TimeEntry.query.filter_by(user_id=user_id)
    
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(TimeEntry.start_time >= start_dt)
        except ValueError:
            return jsonify({'error': 'Invalid start_date format'}), 400
    
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(TimeEntry.start_time <= end_dt)
        except ValueError:
            return jsonify({'error': 'Invalid end_date format'}), 400
    
    if task_id:
        query = query.filter_by(task_id=task_id)
    
    # Execute query with pagination
    entries = query.order_by(TimeEntry.start_time.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'time_entries': [entry.to_dict() for entry in entries.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': entries.total,
            'pages': entries.pages,
            'has_next': entries.has_next,
            'has_prev': entries.has_prev
        }
    }), 200


@time_bp.route('/entries', methods=['POST'])
@login_required
def create_manual_entry():
    """Create a manual time entry."""
    data = request.get_json()
    
    required_fields = ['task_id', 'start_time', 'end_time']
    if not data or not all(field in data for field in required_fields):
        return jsonify({'error': 'Task ID, start time, and end time are required'}), 400
    
    user_id = session['user_id']
    
    # Verify task exists and is active
    task = Task.query.filter_by(id=data['task_id'], is_active=True).first()
    if not task:
        return jsonify({'error': 'Task not found or inactive'}), 404
    
    try:
        start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
    except ValueError:
        return jsonify({'error': 'Invalid datetime format'}), 400
    
    if end_time <= start_time:
        return jsonify({'error': 'End time must be after start time'}), 400
    
    # Create manual time entry
    time_entry = TimeEntry(
        user_id=user_id,
        task_id=data['task_id'],
        start_time=start_time,
        description=data.get('description'),
        is_manual=True
    )
    time_entry.stop_timer(end_time)
    
    db.session.add(time_entry)
    db.session.commit()
    
    return jsonify({
        'message': 'Manual time entry created successfully',
        'time_entry': time_entry.to_dict()
    }), 201
