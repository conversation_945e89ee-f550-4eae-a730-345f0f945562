"""
Task model for tracking work activities.
"""
from datetime import datetime
from timester.app import db


class Task(db.Model):
    """Task model for tracking work activities."""
    
    __tablename__ = 'tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    
    # Relationships
    time_entries = db.relationship('TimeEntry', backref='task', lazy='dynamic',
                                  cascade='all, delete-orphan')
    
    def __init__(self, name, description=None):
        """Initialize a new task."""
        self.name = name
        self.description = description
    
    def get_total_hours(self):
        """Calculate total hours worked on this task."""
        total_seconds = sum(entry.duration_seconds for entry in self.time_entries 
                           if entry.duration_seconds)
        return total_seconds / 3600.0  # Convert to hours
    
    def get_total_hours_for_user(self, user_id):
        """Calculate total hours worked on this task by a specific user."""
        total_seconds = sum(entry.duration_seconds for entry in self.time_entries 
                           if entry.user_id == user_id and entry.duration_seconds)
        return total_seconds / 3600.0  # Convert to hours
    
    def to_dict(self):
        """Convert task to dictionary representation."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'total_hours': self.get_total_hours()
        }
    
    def __repr__(self):
        """String representation of the task."""
        return f'<Task {self.name}>'

