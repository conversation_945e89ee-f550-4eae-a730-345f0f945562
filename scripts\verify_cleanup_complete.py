#!/usr/bin/env python3
"""
Verification script to ensure backward compatibility methods have been completely removed.
"""
import sys
import ast
import os
from pathlib import Path

# Add the src directory to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def find_method_calls_in_file(file_path, method_names):
    """Find calls to specific methods in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST
        tree = ast.parse(content)
        
        found_calls = []
        
        class MethodCallVisitor(ast.NodeVisitor):
            def visit_Call(self, node):
                if isinstance(node.func, ast.Attribute):
                    if node.func.attr in method_names:
                        found_calls.append({
                            'method': node.func.attr,
                            'line': node.lineno,
                            'col': node.col_offset
                        })
                self.generic_visit(node)
        
        visitor = MethodCallVisitor()
        visitor.visit(tree)
        
        return found_calls
    
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return []


def find_method_definitions_in_file(file_path, method_names):
    """Find method definitions in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        found_defs = []
        
        class MethodDefVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                if node.name in method_names:
                    found_defs.append({
                        'method': node.name,
                        'line': node.lineno,
                        'col': node.col_offset
                    })
                self.generic_visit(node)
        
        visitor = MethodDefVisitor()
        visitor.visit(tree)
        
        return found_defs
    
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return []


def scan_directory_for_methods(directory, method_names, check_definitions=False):
    """Scan a directory for method calls or definitions."""
    results = {}
    
    for root, dirs, files in os.walk(directory):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                
                if check_definitions:
                    found = find_method_definitions_in_file(file_path, method_names)
                else:
                    found = find_method_calls_in_file(file_path, method_names)
                
                if found:
                    results[str(file_path)] = found
    
    return results


def test_removed_methods():
    """Test that backward compatibility methods have been removed."""
    print("Testing removal of backward compatibility methods...")
    
    removed_methods = [
        'get_total_hours',
        'get_total_hours_for_user', 
        'get_duration_hours'
    ]
    
    # Check that method definitions are removed from models
    models_dir = project_root / "src" / "timester" / "models"
    method_definitions = scan_directory_for_methods(models_dir, removed_methods, check_definitions=True)
    
    if method_definitions:
        print("✗ Found method definitions that should have been removed:")
        for file_path, methods in method_definitions.items():
            for method in methods:
                print(f"  {file_path}:{method['line']} - {method['method']}()")
        return False
    else:
        print("✓ No backward compatibility method definitions found in models")
    
    # Check for method calls in the entire src directory
    src_dir = project_root / "src"
    method_calls = scan_directory_for_methods(src_dir, removed_methods, check_definitions=False)
    
    if method_calls:
        print("✗ Found calls to removed methods:")
        for file_path, methods in method_calls.items():
            for method in methods:
                print(f"  {file_path}:{method['line']} - {method['method']}()")
        return False
    else:
        print("✓ No calls to removed methods found in source code")
    
    return True


def test_api_response_format():
    """Test that API responses only include duration_seconds."""
    print("Testing API response format...")
    
    from timester.app import create_app, db
    from timester.models.user import User
    from timester.models.task import Task
    from timester.models.time_entry import TimeEntry
    from datetime import datetime, timedelta
    
    app = create_app('testing')
    with app.app_context():
        db.create_all()
        
        # Create test data
        user = User(username='test', email='<EMAIL>', password='test123')
        task = Task(name='Test Task', description='Test')
        db.session.add_all([user, task])
        db.session.commit()
        
        # Create time entry
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(hours=1, minutes=30)
        
        time_entry = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        time_entry.stop_timer(end_time)
        db.session.add(time_entry)
        db.session.commit()
        
        # Test TimeEntry API response
        entry_dict = time_entry.to_dict()
        if 'duration_hours' in entry_dict:
            print("✗ TimeEntry.to_dict() still includes duration_hours")
            return False
        if 'duration_seconds' not in entry_dict:
            print("✗ TimeEntry.to_dict() missing duration_seconds")
            return False
        
        # Test Task API response
        task_dict = task.to_dict()
        if 'total_hours' in task_dict:
            print("✗ Task.to_dict() still includes total_hours")
            return False
        if 'total_seconds' not in task_dict:
            print("✗ Task.to_dict() missing total_seconds")
            return False
        
        print("✓ API response format is correct")
        
        # Clean up
        db.drop_all()
        
    return True


def test_seconds_based_calculations():
    """Test that all calculations use seconds as primary unit."""
    print("Testing seconds-based calculations...")
    
    from timester.app import create_app, db
    from timester.models.user import User
    from timester.models.task import Task
    from timester.models.time_entry import TimeEntry
    from datetime import datetime, timedelta
    
    app = create_app('testing')
    with app.app_context():
        db.create_all()
        
        # Create test data
        user = User(username='test', email='<EMAIL>', password='test123')
        task = Task(name='Test Task', description='Test')
        db.session.add_all([user, task])
        db.session.commit()
        
        # Create time entries with specific durations
        start_time = datetime.utcnow()
        
        # Entry 1: 1 hour (3600 seconds)
        entry1 = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        entry1.stop_timer(start_time + timedelta(hours=1))
        
        # Entry 2: 30 minutes (1800 seconds)
        entry2 = TimeEntry(user_id=user.id, task_id=task.id, start_time=start_time)
        entry2.stop_timer(start_time + timedelta(minutes=30))
        
        db.session.add_all([entry1, entry2])
        db.session.commit()
        
        # Test calculations
        total_seconds = task.get_total_seconds()
        user_seconds = task.get_total_seconds_for_user(user.id)
        
        expected_total = 3600 + 1800  # 5400 seconds
        
        if total_seconds != expected_total:
            print(f"✗ Task.get_total_seconds() returned {total_seconds}, expected {expected_total}")
            return False
        
        if user_seconds != expected_total:
            print(f"✗ Task.get_total_seconds_for_user() returned {user_seconds}, expected {expected_total}")
            return False
        
        print("✓ Seconds-based calculations are working correctly")
        
        # Clean up
        db.drop_all()
        
    return True


def main():
    """Run all cleanup verification tests."""
    print("🧹 Verifying Backward Compatibility Cleanup")
    print("=" * 50)
    
    tests = [
        ("Removed Methods", test_removed_methods),
        ("API Response Format", test_api_response_format),
        ("Seconds-based Calculations", test_seconds_based_calculations),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Cleanup Verification Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 Cleanup verification successful!")
        print("\nBackward compatibility methods have been completely removed:")
        print("- get_total_hours() removed from Task model")
        print("- get_total_hours_for_user() removed from Task model") 
        print("- get_duration_hours() removed from TimeEntry model")
        print("- All API responses use seconds-based data only")
        print("- All calculations use seconds as primary unit")
        print("- No references to removed methods in codebase")
    else:
        print("⚠️  Some cleanup verification tests failed.")
        print("Please review the issues above.")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
