# Ignore the IGNORE folder
IGNORE/

# Project Folders
timester/screenshots/
screenshots/
logs/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Virtual Environment
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Flask
instance/
.webassets-cache

# Database
*.db
*.sqlite3
migrations/versions/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Frontend build
frontend/dist/
frontend/.cache/

# Logs
*.log

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Timester agent config
.timester/

