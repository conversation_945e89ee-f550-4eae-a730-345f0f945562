"""
User model for authentication and role management.
"""
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from timester.app import db


class User(db.Model):
    """User model for team members and administrators."""
    
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.Enum('team_member', 'administrator', name='user_roles'), 
                     nullable=False, default='team_member')
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, 
                          onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    time_entries = db.relationship('TimeEntry', backref='user', lazy='dynamic',
                                  cascade='all, delete-orphan')
    screenshots = db.relationship('Screenshot', backref='user', lazy='dynamic',
                                 cascade='all, delete-orphan')
    
    def __init__(self, username, email, password, role='team_member'):
        """Initialize a new user."""
        self.username = username
        self.email = email
        self.set_password(password)
        self.role = role
    
    def set_password(self, password):
        """Set the user's password hash."""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if the provided password matches the user's password."""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """Check if the user is an administrator."""
        return self.role == 'administrator'
    
    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary representation."""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_sensitive:
            # Only include sensitive data when explicitly requested
            pass
        
        return data
    
    def __repr__(self):
        """String representation of the user."""
        return f'<User {self.username}>'
