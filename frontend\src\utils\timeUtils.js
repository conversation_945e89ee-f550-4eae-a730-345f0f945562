/**
 * Time utility functions for duration formatting and parsing
 * All functions work with seconds as the primary unit
 */

/**
 * Format duration in seconds to HH:MM:SS format
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted time string (e.g., "02:30:45")
 */
export function formatDurationHMS(seconds) {
  if (!seconds || seconds < 0) return '00:00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * Format duration in seconds to HH.MM decimal hours format
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted decimal hours (e.g., "2.51")
 */
export function formatDurationDecimal(seconds) {
  if (!seconds || seconds < 0) return '0.00'
  
  const hours = seconds / 3600
  return hours.toFixed(2)
}

/**
 * Format duration in seconds to human-readable format (e.g., "2h 30m")
 * @param {number} seconds - Duration in seconds
 * @returns {string} Human-readable duration
 */
export function formatDurationHuman(seconds) {
  if (!seconds || seconds < 0) return '0m'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours === 0) {
    return `${minutes}m`
  } else if (minutes === 0) {
    return `${hours}h`
  } else {
    return `${hours}h ${minutes}m`
  }
}

/**
 * Format duration in seconds to total minutes (for short durations)
 * @param {number} seconds - Duration in seconds
 * @returns {string} Total minutes (e.g., "150 min")
 */
export function formatDurationMinutes(seconds) {
  if (!seconds || seconds < 0) return '0 min'
  
  const totalMinutes = Math.round(seconds / 60)
  return `${totalMinutes} min`
}

/**
 * Parse time string (HH:MM or HH:MM:SS) to seconds
 * @param {string} timeString - Time string to parse
 * @returns {number} Duration in seconds
 */
export function parseDurationToSeconds(timeString) {
  if (!timeString || typeof timeString !== 'string') return 0
  
  const parts = timeString.trim().split(':')
  
  if (parts.length === 2) {
    // HH:MM format
    const hours = parseInt(parts[0], 10) || 0
    const minutes = parseInt(parts[1], 10) || 0
    return hours * 3600 + minutes * 60
  } else if (parts.length === 3) {
    // HH:MM:SS format
    const hours = parseInt(parts[0], 10) || 0
    const minutes = parseInt(parts[1], 10) || 0
    const seconds = parseInt(parts[2], 10) || 0
    return hours * 3600 + minutes * 60 + seconds
  }
  
  return 0
}

/**
 * Parse decimal hours to seconds
 * @param {string|number} hoursString - Decimal hours (e.g., "2.5" or 2.5)
 * @returns {number} Duration in seconds
 */
export function parseDecimalHoursToSeconds(hoursString) {
  const hours = parseFloat(hoursString)
  if (isNaN(hours) || hours < 0) return 0
  return Math.round(hours * 3600)
}

/**
 * Format seconds to time input value (HH:MM format for HTML time inputs)
 * @param {number} seconds - Duration in seconds
 * @returns {string} Time string in HH:MM format
 */
export function formatSecondsToTimeInput(seconds) {
  if (!seconds || seconds < 0) return '00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

/**
 * Calculate duration between two date strings
 * @param {string} startTime - ISO date string
 * @param {string} endTime - ISO date string
 * @returns {number} Duration in seconds
 */
export function calculateDuration(startTime, endTime) {
  if (!startTime || !endTime) return 0
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0
  
  const diffMs = end.getTime() - start.getTime()
  return Math.max(0, Math.floor(diffMs / 1000))
}

/**
 * Get current duration for an active timer
 * @param {string} startTime - ISO date string when timer started
 * @returns {number} Current duration in seconds
 */
export function getCurrentDuration(startTime) {
  if (!startTime) return 0
  
  const start = new Date(startTime)
  const now = new Date()
  
  if (isNaN(start.getTime())) return 0
  
  const diffMs = now.getTime() - start.getTime()
  return Math.max(0, Math.floor(diffMs / 1000))
}

/**
 * Validate time input string
 * @param {string} timeString - Time string to validate
 * @returns {boolean} True if valid time format
 */
export function isValidTimeFormat(timeString) {
  if (!timeString || typeof timeString !== 'string') return false
  
  // Match HH:MM or HH:MM:SS format
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9])(:([0-5]?[0-9]))?$/
  return timeRegex.test(timeString.trim())
}

/**
 * Format duration with user preference (HH:MM:SS or decimal)
 * @param {number} seconds - Duration in seconds
 * @param {string} format - 'hms' for HH:MM:SS, 'decimal' for decimal hours, 'human' for human readable
 * @returns {string} Formatted duration
 */
export function formatDuration(seconds, format = 'hms') {
  switch (format) {
    case 'decimal':
      return formatDurationDecimal(seconds)
    case 'human':
      return formatDurationHuman(seconds)
    case 'minutes':
      return formatDurationMinutes(seconds)
    case 'hms':
    default:
      return formatDurationHMS(seconds)
  }
}

/**
 * Sum an array of durations in seconds
 * @param {number[]} durations - Array of durations in seconds
 * @returns {number} Total duration in seconds
 */
export function sumDurations(durations) {
  if (!Array.isArray(durations)) return 0
  return durations.reduce((total, duration) => total + (duration || 0), 0)
}

/**
 * Get duration display preferences from localStorage
 * @returns {string} Format preference ('hms', 'decimal', 'human')
 */
export function getDurationDisplayPreference() {
  return localStorage.getItem('timester_duration_format') || 'hms'
}

/**
 * Set duration display preferences in localStorage
 * @param {string} format - Format preference ('hms', 'decimal', 'human')
 */
export function setDurationDisplayPreference(format) {
  localStorage.setItem('timester_duration_format', format)
}
