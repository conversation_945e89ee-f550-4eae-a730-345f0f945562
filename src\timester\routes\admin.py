"""
Admin routes for user management, task management, and reporting.
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, session
from sqlalchemy import func, and_
from timester.app import db
from timester.models.user import User
from timester.models.task import Task
from timester.models.time_entry import TimeEntry
from timester.models.screenshot import Screenshot
from timester.utils.auth import admin_required

admin_bp = Blueprint('admin', __name__)


@admin_bp.route('/users', methods=['GET'])
@admin_required
def get_users():
    """Get all users."""
    users = User.query.all()
    return jsonify({
        'users': [user.to_dict() for user in users]
    }), 200


@admin_bp.route('/users', methods=['POST'])
@admin_required
def create_user():
    """Create a new user."""
    data = request.get_json()
    
    required_fields = ['username', 'email', 'password']
    if not data or not all(field in data for field in required_fields):
        return jsonify({'error': 'Username, email, and password are required'}), 400
    
    # Check if username or email already exists
    existing_user = User.query.filter(
        (User.username == data['username']) | (User.email == data['email'])
    ).first()
    
    if existing_user:
        return jsonify({'error': 'Username or email already exists'}), 400
    
    if len(data['password']) < 8:
        return jsonify({'error': 'Password must be at least 8 characters long'}), 400
    
    # Create new user
    user = User(
        username=data['username'],
        email=data['email'],
        password=data['password'],
        role=data.get('role', 'team_member')
    )
    
    db.session.add(user)
    db.session.commit()
    
    return jsonify({
        'message': 'User created successfully',
        'user': user.to_dict()
    }), 201


@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@admin_required
def update_user(user_id):
    """Update user information."""
    user = User.query.get_or_404(user_id)
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    # Update allowed fields
    if 'username' in data:
        # Check if username is already taken by another user
        existing = User.query.filter(
            User.username == data['username'],
            User.id != user_id
        ).first()
        if existing:
            return jsonify({'error': 'Username already exists'}), 400
        user.username = data['username']
    
    if 'email' in data:
        # Check if email is already taken by another user
        existing = User.query.filter(
            User.email == data['email'],
            User.id != user_id
        ).first()
        if existing:
            return jsonify({'error': 'Email already exists'}), 400
        user.email = data['email']
    
    if 'role' in data:
        if data['role'] in ['team_member', 'administrator']:
            user.role = data['role']
        else:
            return jsonify({'error': 'Invalid role'}), 400
    
    if 'is_active' in data:
        user.is_active = bool(data['is_active'])
    
    db.session.commit()
    
    return jsonify({
        'message': 'User updated successfully',
        'user': user.to_dict()
    }), 200


@admin_bp.route('/tasks', methods=['GET'])
@admin_required
def get_tasks():
    """Get all tasks."""
    tasks = Task.query.all()
    return jsonify({
        'tasks': [task.to_dict() for task in tasks]
    }), 200


@admin_bp.route('/tasks', methods=['POST'])
@admin_required
def create_task():
    """Create a new task."""
    data = request.get_json()
    
    if not data or not data.get('name'):
        return jsonify({'error': 'Task name is required'}), 400
    
    task = Task(
        name=data['name'],
        description=data.get('description')
    )
    
    db.session.add(task)
    db.session.commit()
    
    return jsonify({
        'message': 'Task created successfully',
        'task': task.to_dict()
    }), 201


@admin_bp.route('/tasks/<int:task_id>', methods=['PUT'])
@admin_required
def update_task(task_id):
    """Update task information."""
    task = Task.query.get_or_404(task_id)
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    if 'name' in data:
        task.name = data['name']
    
    if 'description' in data:
        task.description = data['description']
    
    if 'is_active' in data:
        task.is_active = bool(data['is_active'])
    
    db.session.commit()
    
    return jsonify({
        'message': 'Task updated successfully',
        'task': task.to_dict()
    }), 200


@admin_bp.route('/time-entries', methods=['GET'])
@admin_required
def get_all_time_entries():
    """Get time entries for all users with filtering options."""
    # Parse query parameters
    user_id = request.args.get('user_id')
    task_id = request.args.get('task_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 100)
    
    # Build query
    query = TimeEntry.query
    
    if user_id:
        query = query.filter_by(user_id=user_id)
    
    if task_id:
        query = query.filter_by(task_id=task_id)
    
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(TimeEntry.start_time >= start_dt)
        except ValueError:
            return jsonify({'error': 'Invalid start_date format'}), 400
    
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(TimeEntry.start_time <= end_dt)
        except ValueError:
            return jsonify({'error': 'Invalid end_date format'}), 400
    
    # Execute query with pagination
    entries = query.order_by(TimeEntry.start_time.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'time_entries': [entry.to_dict() for entry in entries.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': entries.total,
            'pages': entries.pages,
            'has_next': entries.has_next,
            'has_prev': entries.has_prev
        }
    }), 200
