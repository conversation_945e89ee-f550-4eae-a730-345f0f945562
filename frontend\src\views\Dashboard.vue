<template>
  <div class="dashboard">
    <div class="card">
      <div class="card-header">
        <h2>Welcome, {{ authStore.user?.username }}!</h2>
      </div>

      <div class="dashboard-content">
        <div v-if="authStore.isTeamMember" class="team-member-dashboard">
          <div class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="action-buttons">
              <router-link to="/time-tracking" class="btn btn-primary">
                Start Time Tracking
              </router-link>
            </div>
          </div>

          <div class="current-timer" v-if="currentTimer">
            <h3>Current Timer</h3>
            <div class="timer-info">
              <p><strong>Task:</strong> {{ currentTimer.task_name }}</p>
              <p><strong>Started:</strong> {{ formatTime(currentTimer.start_time) }}</p>
              <p><strong>Duration:</strong> {{ formatDuration(currentTimer.duration) }}</p>
            </div>
          </div>

          <div class="recent-entries">
            <h3>Recent Time Entries</h3>
            <div v-if="recentEntries.length === 0" class="no-data">
              No time entries yet. Start tracking your time!
            </div>
            <div v-else class="entries-list">
              <div v-for="entry in recentEntries" :key="entry.id" class="entry-item">
                <div class="entry-info">
                  <strong>{{ entry.task_name }}</strong>
                  <span class="entry-date">{{ formatDate(entry.start_time) }}</span>
                </div>
                <div class="entry-duration">
                  {{ formatDuration(entry.duration_hours * 3600) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="authStore.isAdmin" class="admin-dashboard">
          <div class="quick-actions">
            <h3>Admin Quick Actions</h3>
            <div class="action-buttons">
              <router-link to="/admin" class="btn btn-primary">
                Admin Panel
              </router-link>
            </div>
          </div>

          <div class="stats-overview">
            <h3>Team Overview</h3>
            <div class="stats-grid">
              <div class="stat-card">
                <h4>Active Users</h4>
                <p class="stat-number">{{ stats.activeUsers || 0 }}</p>
              </div>
              <div class="stat-card">
                <h4>Total Hours Today</h4>
                <p class="stat-number">{{ stats.hoursToday || 0 }}</p>
              </div>
              <div class="stat-card">
                <h4>Active Tasks</h4>
                <p class="stat-number">{{ stats.activeTasks || 0 }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import api from '../services/api'

export default {
  name: 'Dashboard',
  setup() {
    const authStore = useAuthStore()
    const currentTimer = ref(null)
    const recentEntries = ref([])
    const stats = ref({})

    const loadDashboardData = async () => {
      try {
        if (authStore.isTeamMember) {
          // Load current timer
          const timerResponse = await api.get('/time/current')
          currentTimer.value = timerResponse.data.active_timer

          // Load recent entries
          const entriesResponse = await api.get('/time/entries?per_page=5')
          recentEntries.value = entriesResponse.data.time_entries
        }

        if (authStore.isAdmin) {
          // Load admin stats
          // TODO: Implement admin stats endpoint
          stats.value = {
            activeUsers: 5,
            hoursToday: 32,
            activeTasks: 8
          }
        }
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
      }
    }

    const formatTime = (timeString) => {
      return new Date(timeString).toLocaleTimeString()
    }

    const formatDate = (timeString) => {
      return new Date(timeString).toLocaleDateString()
    }

    const formatDuration = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}h ${minutes}m`
    }

    onMounted(() => {
      loadDashboardData()
    })

    return {
      authStore,
      currentTimer,
      recentEntries,
      stats,
      formatTime,
      formatDate,
      formatDuration
    }
  }
}
</script>

<style scoped>
.dashboard-content {
  margin-top: 1rem;
}

.quick-actions {
  margin-bottom: 2rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.current-timer {
  background-color: #e8f5e8;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 2rem;
}

.timer-info p {
  margin: 0.5rem 0;
}

.recent-entries {
  margin-bottom: 2rem;
}

.entries-list {
  margin-top: 1rem;
}

.entry-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.entry-info {
  display: flex;
  flex-direction: column;
}

.entry-date {
  font-size: 0.9rem;
  color: #666;
}

.entry-duration {
  font-weight: bold;
  color: #27ae60;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.stat-card {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  text-align: center;
}

.stat-card h4 {
  margin: 0 0 0.5rem 0;
  color: #666;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  color: #2c3e50;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}
</style>
