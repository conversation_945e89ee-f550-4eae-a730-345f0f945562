# Timester Development Makefile

.PHONY: help install install-dev setup test lint format clean run-backend run-frontend run-agent build

# Default target
help:
	@echo "Timester Development Commands"
	@echo "============================"
	@echo "setup          - Set up development environment"
	@echo "install        - Install production dependencies"
	@echo "install-dev    - Install development dependencies"
	@echo "test           - Run tests"
	@echo "lint           - Run linting"
	@echo "format         - Format code"
	@echo "clean          - Clean build artifacts"
	@echo "run-backend    - Run Flask backend"
	@echo "run-frontend   - Run Vue.js frontend"
	@echo "run-agent      - Run desktop agent"
	@echo "build          - Build for production"

# Setup development environment
setup:
	@echo "Setting up development environment..."
	python scripts/setup_dev.py

# Install dependencies
install:
	pip install -e .

install-dev:
	pip install -e ".[dev]"
	cd frontend && npm install

# Testing
test:
	pytest tests/ -v

test-coverage:
	pytest tests/ --cov=timester --cov-report=html

# Code quality
lint:
	flake8 src/timester tests/
	cd frontend && npm run lint

format:
	black src/timester tests/
	cd frontend && npm run lint --fix

# Clean up
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	cd frontend && rm -rf dist/ node_modules/.cache/

# Development servers
run-backend:
	flask run --debug

run-frontend:
	cd frontend && npm run dev

run-agent:
	python -m timester.agent.main

# Production build
build:
	cd frontend && npm run build
	pip install build
	python -m build

# Database operations
db-init:
	flask init-db

db-migrate:
	flask db migrate

db-upgrade:
	flask db upgrade

# Docker operations (if using Docker)
docker-build:
	docker build -t timester .

docker-run:
	docker run -p 5000:5000 timester

# Development helpers
create-admin:
	flask create-admin

create-sample-data:
	flask create-sample-tasks
