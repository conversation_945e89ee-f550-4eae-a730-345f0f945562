# Timester Development Makefile

.PHONY: help install install-dev setup test lint format clean run-backend run-frontend run-agent build

# Default target
help:
	@echo "Timester Development Commands"
	@echo "============================"
	@echo "setup          - Set up development environment"
	@echo "install        - Install production dependencies"
	@echo "install-dev    - Install development dependencies"
	@echo "test           - Run tests"
	@echo "lint           - Run linting"
	@echo "format         - Format code"
	@echo "clean          - Clean build artifacts"
	@echo "run-backend    - Run Flask backend"
	@echo "run-frontend   - Run Vue.js frontend"
	@echo "run-agent      - Run desktop agent"
	@echo "build          - Build for production"

# Setup development environment
setup:
	@echo "Setting up development environment..."
	poetry run python scripts/setup_dev.py

# Install dependencies
install:
	poetry install

install-dev:
	poetry install --with dev
	cd frontend && npm install

# Testing
test:
	poetry run pytest tests/ -v

test-coverage:
	poetry run pytest tests/ --cov=timester --cov-report=html

# Code quality
lint:
	poetry run flake8 src/timester tests/
	cd frontend && npm run lint

format:
	poetry run black src/timester tests/
	cd frontend && npm run lint --fix

# Clean up
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	cd frontend && rm -rf dist/ node_modules/.cache/

# Development servers
run-backend:
	poetry run flask run --debug

run-frontend:
	cd frontend && npm run dev

run-agent:
	poetry run timester-agent

# Production build
build:
	cd frontend && npm run build
	poetry build

# Database operations
db-init:
	poetry run flask init-db

db-migrate:
	poetry run flask db migrate

db-upgrade:
	poetry run flask db upgrade

# Docker operations (if using Docker)
docker-build:
	docker build -t timester .

docker-run:
	docker run -p 5000:5000 timester

# Development helpers
create-admin:
	poetry run flask create-admin

create-sample-data:
	poetry run flask create-sample-tasks

# Poetry specific commands
poetry-shell:
	poetry shell

poetry-update:
	poetry update

poetry-show:
	poetry show
