"""
Storage service for managing DigitalOcean Spaces integration.
"""
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from botocore.client import Config
from flask import current_app
import logging

logger = logging.getLogger(__name__)


class StorageService:
    """Service for managing file storage in DigitalOcean Spaces."""
    
    def __init__(self):
        """Initialize the storage service."""
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the S3-compatible client for DigitalOcean Spaces."""
        try:
            self.client = boto3.client(
                's3',
                region_name=current_app.config['DO_SPACES_REGION'],
                endpoint_url=current_app.config['DO_SPACES_ENDPOINT'],
                aws_access_key_id=current_app.config['DO_SPACES_KEY'],
                aws_secret_access_key=current_app.config['DO_SPACES_SECRET'],
                config=Config(signature_version='s3v4')
            )
        except Exception as e:
            logger.error(f"Failed to initialize storage client: {e}")
            self.client = None
    
    def upload_file(self, file_obj, object_name, content_type=None):
        """
        Upload a file to DigitalOcean Spaces.
        
        Args:
            file_obj: File object to upload
            object_name: Name of the object in the bucket
            content_type: MIME type of the file
        
        Returns:
            bool: True if upload was successful, False otherwise
        """
        if not self.client:
            logger.error("Storage client not initialized")
            return False
        
        try:
            # Determine content type if not provided
            if not content_type:
                if object_name.lower().endswith('.png'):
                    content_type = 'image/png'
                elif object_name.lower().endswith(('.jpg', '.jpeg')):
                    content_type = 'image/jpeg'
                else:
                    content_type = 'application/octet-stream'
            
            # Upload the file
            self.client.upload_fileobj(
                file_obj,
                current_app.config['DO_SPACES_BUCKET'],
                object_name,
                ExtraArgs={
                    'ContentType': content_type,
                    'ACL': 'private'  # Keep files private
                }
            )
            
            logger.info(f"Successfully uploaded {object_name}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to upload {object_name}: {e}")
            return False
        except NoCredentialsError:
            logger.error("Storage credentials not found")
            return False
        except Exception as e:
            logger.error(f"Unexpected error uploading {object_name}: {e}")
            return False
    
    def generate_signed_url(self, object_name, expiration=3600):
        """
        Generate a signed URL for accessing a private object.
        
        Args:
            object_name: Name of the object in the bucket
            expiration: URL expiration time in seconds (default: 1 hour)
        
        Returns:
            str: Signed URL if successful, None otherwise
        """
        if not self.client:
            logger.error("Storage client not initialized")
            return None
        
        try:
            url = self.client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': current_app.config['DO_SPACES_BUCKET'],
                    'Key': object_name
                },
                ExpiresIn=expiration
            )
            
            return url
            
        except ClientError as e:
            logger.error(f"Failed to generate signed URL for {object_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error generating signed URL for {object_name}: {e}")
            return None
    
    def delete_file(self, object_name):
        """
        Delete a file from DigitalOcean Spaces.
        
        Args:
            object_name: Name of the object to delete
        
        Returns:
            bool: True if deletion was successful, False otherwise
        """
        if not self.client:
            logger.error("Storage client not initialized")
            return False
        
        try:
            self.client.delete_object(
                Bucket=current_app.config['DO_SPACES_BUCKET'],
                Key=object_name
            )
            
            logger.info(f"Successfully deleted {object_name}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to delete {object_name}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting {object_name}: {e}")
            return False
    
    def file_exists(self, object_name):
        """
        Check if a file exists in DigitalOcean Spaces.
        
        Args:
            object_name: Name of the object to check
        
        Returns:
            bool: True if file exists, False otherwise
        """
        if not self.client:
            logger.error("Storage client not initialized")
            return False
        
        try:
            self.client.head_object(
                Bucket=current_app.config['DO_SPACES_BUCKET'],
                Key=object_name
            )
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            logger.error(f"Error checking if {object_name} exists: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking if {object_name} exists: {e}")
            return False
